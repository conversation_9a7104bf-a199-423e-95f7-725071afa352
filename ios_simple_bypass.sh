#!/bin/bash
# ios_simple_bypass.sh
# iOS设备端简化验证码绕过脚本
# 适用于已越狱的iOS设备，在设备上直接运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_header() {
    echo "================================================"
    echo "📱 iOS设备端验证码绕过工具"
    echo "================================================"
    echo ""
}

# 检查是否在iOS设备上运行
check_ios_device() {
    if [[ $(uname) != "Darwin" ]] || [[ ! -d "/var/mobile" ]]; then
        log_error "此脚本需要在越狱的iOS设备上运行"
        echo "如果您在电脑上，请使用: python3 timelock_bypass_generator.py"
        exit 1
    fi
    
    log_success "检测到iOS设备环境"
}

# 查找应用目录
find_app_directory() {
    log_info "查找Sky-iOS-Gold应用目录..."
    
    # 常见的应用安装路径
    local app_paths=(
        "/var/containers/Bundle/Application/*/Sky-iOS-Gold.app"
        "/Applications/Sky-iOS-Gold.app"
        "/var/mobile/Applications/*/Sky-iOS-Gold.app"
    )
    
    for path_pattern in "${app_paths[@]}"; do
        for app_dir in $path_pattern; do
            if [[ -d "$app_dir" ]] && [[ -f "$app_dir/hhhhsd.dylib" ]]; then
                APP_DIR="$app_dir"
                log_success "找到应用目录: $APP_DIR"
                return 0
            fi
        done
    done
    
    log_error "未找到Sky-iOS-Gold应用目录"
    echo "请手动指定应用路径："
    read -p "输入应用目录路径: " APP_DIR
    
    if [[ ! -d "$APP_DIR" ]] || [[ ! -f "$APP_DIR/hhhhsd.dylib" ]]; then
        log_error "无效的应用目录"
        exit 1
    fi
}

# 备份原始文件
backup_files() {
    log_info "备份原始文件..."
    
    cd "$APP_DIR"
    
    if [[ ! -f "hhhhsd.dylib.backup" ]]; then
        cp "hhhhsd.dylib" "hhhhsd.dylib.backup"
        log_success "已备份: hhhhsd.dylib -> hhhhsd.dylib.backup"
    else
        log_warning "备份文件已存在"
    fi
}

# 检查Substrate环境
check_substrate() {
    log_info "检查Substrate环境..."
    
    if [[ -f "/Library/MobileSubstrate/MobileSubstrate.dylib" ]]; then
        log_success "Substrate环境正常"
        return 0
    else
        log_error "未找到Substrate，请安装Cydia Substrate"
        return 1
    fi
}

# 简单的Hook注入（使用预编译的dylib）
inject_hook() {
    log_info "注入Hook..."
    
    # 检查是否有预编译的TimeLockBypass.dylib
    local hook_dylib="/Library/MobileSubstrate/DynamicLibraries/TimeLockBypass.dylib"
    local hook_plist="/Library/MobileSubstrate/DynamicLibraries/TimeLockBypass.plist"
    
    if [[ -f "$hook_dylib" ]]; then
        log_success "找到已安装的TimeLockBypass"
        return 0
    fi
    
    log_warning "未找到预编译的Hook文件"
    echo "请在电脑上编译并安装，或者使用手动补丁方案"
    
    return 1
}

# 手动字符串替换（简单的补丁方法）
apply_string_patch() {
    log_info "应用字符串补丁..."
    
    cd "$APP_DIR"
    
    # 备份
    if [[ ! -f "hhhhsd.dylib.original" ]]; then
        cp "hhhhsd.dylib" "hhhhsd.dylib.original"
    fi
    
    # 查找并替换验证相关字符串
    # 这是一个简化的方法，将验证相关的字符串替换为无害的字符串
    
    log_info "查找验证相关字符串..."
    
    # 使用sed替换一些可能的验证字符串
    # 注意：这种方法风险较高，可能导致应用崩溃
    
    local temp_file="hhhhsd_temp.dylib"
    cp "hhhhsd.dylib" "$temp_file"
    
    # 替换一些可能的验证字符串（需要根据实际情况调整）
    # 这里只是示例，实际使用时需要分析具体的字符串
    
    log_warning "⚠️  字符串补丁是实验性功能，可能导致应用不稳定"
    read -p "确认继续？(y/N): " -r
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        mv "$temp_file" "hhhhsd.dylib"
        log_success "字符串补丁已应用"
    else
        rm -f "$temp_file"
        log_info "跳过字符串补丁"
    fi
}

# 重启应用
restart_app() {
    log_info "重启Sky-iOS-Gold应用..."
    
    # 查找应用进程
    local app_pid=$(ps aux | grep "Sky-iOS-Gold" | grep -v grep | awk '{print $2}' | head -1)
    
    if [[ -n "$app_pid" ]]; then
        log_info "找到应用进程: $app_pid"
        kill "$app_pid"
        log_success "应用已终止，请手动重新启动"
    else
        log_info "应用未运行，请手动启动"
    fi
}

# 检查效果
check_result() {
    log_info "检查绕过效果..."
    
    echo ""
    echo "📱 请执行以下步骤验证："
    echo "1. 启动Sky-iOS-Gold应用"
    echo "2. 尝试使用需要验证码的功能"
    echo "3. 观察是否还有验证码弹窗"
    echo ""
    
    echo "如果仍有弹窗，可以尝试："
    echo "1. 重新运行此脚本"
    echo "2. 使用电脑端的完整工具"
    echo "3. 恢复原始文件: cp hhhhsd.dylib.backup hhhhsd.dylib"
}

# 恢复原始文件
restore_backup() {
    log_info "恢复原始文件..."
    
    cd "$APP_DIR"
    
    if [[ -f "hhhhsd.dylib.backup" ]]; then
        cp "hhhhsd.dylib.backup" "hhhhsd.dylib"
        log_success "已恢复原始文件"
    else
        log_error "未找到备份文件"
        return 1
    fi
}

# 显示菜单
show_menu() {
    echo ""
    echo "📋 请选择操作："
    echo "1. 检查并注入Hook（推荐）"
    echo "2. 应用字符串补丁（实验性）"
    echo "3. 重启应用"
    echo "4. 检查绕过效果"
    echo "5. 恢复原始文件"
    echo "6. 显示应用信息"
    echo "0. 退出"
    echo ""
}

# 显示应用信息
show_app_info() {
    log_info "应用信息："
    echo "应用目录: $APP_DIR"
    echo "原始文件: $(ls -la "$APP_DIR/hhhhsd.dylib" 2>/dev/null || echo '不存在')"
    echo "备份文件: $(ls -la "$APP_DIR/hhhhsd.dylib.backup" 2>/dev/null || echo '不存在')"
    echo ""
    
    if [[ -f "$APP_DIR/hhhhsd.dylib" ]]; then
        local file_size=$(stat -f%z "$APP_DIR/hhhhsd.dylib" 2>/dev/null)
        echo "文件大小: $file_size 字节"
    fi
}

# 主函数
main() {
    show_header
    
    check_ios_device
    find_app_directory
    backup_files
    
    while true; do
        show_menu
        read -p "请输入选择 (0-6): " choice
        
        case $choice in
            1)
                if check_substrate && inject_hook; then
                    restart_app
                    check_result
                else
                    log_warning "Hook注入失败，尝试字符串补丁方案"
                fi
                ;;
            2)
                apply_string_patch
                restart_app
                check_result
                ;;
            3)
                restart_app
                ;;
            4)
                check_result
                ;;
            5)
                restore_backup
                restart_app
                ;;
            6)
                show_app_info
                ;;
            0)
                log_success "退出程序"
                break
                ;;
            *)
                log_error "无效选择"
                ;;
        esac
    done
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
