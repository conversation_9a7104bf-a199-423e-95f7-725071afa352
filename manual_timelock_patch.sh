#!/bin/bash
# manual_timelock_patch.sh
# TimeLock验证码手动二进制补丁脚本
# 
# 功能：直接修改hhhhsd.dylib文件，绕过验证码弹窗
# 警告：这是高风险操作，请确保已备份原文件
# 版本：1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
APP_PATH="."
DYLIB_PATH="$APP_PATH/hhhhsd.dylib"
BACKUP_PATH="$APP_PATH/hhhhsd.dylib.backup"
PATCH_LOG="timelock_patch.log"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$PATCH_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$PATCH_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$PATCH_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$PATCH_LOG"
}

# 显示标题
show_header() {
    echo "=================================================================="
    echo "🔧 TimeLock验证码手动二进制补丁工具"
    echo "=================================================================="
    echo ""
    echo "⚠️  警告：这是高风险操作，可能导致应用无法启动"
    echo "📋 请确保已阅读并理解操作风险"
    echo ""
}

# 检查环境
check_environment() {
    log_info "检查补丁环境..."
    
    # 检查目标文件
    if [ ! -f "$DYLIB_PATH" ]; then
        log_error "未找到目标文件: $DYLIB_PATH"
        return 1
    fi
    
    log_success "找到目标文件: $DYLIB_PATH"
    
    # 检查文件大小
    local file_size=$(stat -f%z "$DYLIB_PATH" 2>/dev/null || stat -c%s "$DYLIB_PATH" 2>/dev/null)
    log_info "文件大小: $file_size 字节"
    
    # 检查必要工具
    local tools=("hexdump" "xxd" "codesign")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "未找到必要工具: $tool"
            return 1
        fi
    done
    
    log_success "环境检查通过"
    return 0
}

# 备份原文件
backup_original() {
    log_info "备份原始文件..."
    
    if [ -f "$BACKUP_PATH" ]; then
        log_warning "备份文件已存在: $BACKUP_PATH"
        read -p "是否覆盖现有备份？(y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过备份"
            return 0
        fi
    fi
    
    cp "$DYLIB_PATH" "$BACKUP_PATH"
    log_success "备份完成: $DYLIB_PATH -> $BACKUP_PATH"
    
    # 验证备份
    if cmp -s "$DYLIB_PATH" "$BACKUP_PATH"; then
        log_success "备份验证通过"
    else
        log_error "备份验证失败"
        return 1
    fi
    
    return 0
}

# 分析目标函数
analyze_target_functions() {
    log_info "分析目标函数..."
    
    # 使用nm或objdump分析符号
    if command -v nm &> /dev/null; then
        log_info "使用nm分析符号表..."
        nm "$DYLIB_PATH" | grep -i "timelock\|alert" > "symbols_analysis.txt" 2>/dev/null || true
        
        if [ -s "symbols_analysis.txt" ]; then
            log_info "找到相关符号："
            head -10 "symbols_analysis.txt"
        else
            log_warning "未找到相关符号"
        fi
    fi
    
    # 搜索关键字符串
    log_info "搜索关键字符串..."
    strings "$DYLIB_PATH" | grep -i "验证\|code\|alert\|timelock" > "strings_analysis.txt" 2>/dev/null || true
    
    if [ -s "strings_analysis.txt" ]; then
        log_info "找到相关字符串："
        head -10 "strings_analysis.txt"
    else
        log_warning "未找到相关字符串"
    fi
}

# 查找函数地址
find_function_addresses() {
    log_info "查找目标函数地址..."
    
    # 这里需要根据具体的二进制分析结果来确定
    # 由于没有具体的地址信息，这里提供一个框架
    
    log_warning "⚠️  需要手动分析确定函数地址"
    echo ""
    echo "请使用以下工具分析hhhhsd.dylib："
    echo "1. Hopper Disassembler"
    echo "2. IDA Pro"
    echo "3. Ghidra"
    echo "4. radare2"
    echo ""
    echo "查找目标："
    echo "- TimeLock.showAlert 方法"
    echo "- TimeLock.showAlert2 方法"
    echo "- 相关的验证码弹窗逻辑"
    echo ""
    
    read -p "是否已经确定了函数地址？(y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "请先使用反汇编工具分析目标函数地址"
        return 1
    fi
    
    return 0
}

# 应用二进制补丁
apply_binary_patch() {
    log_info "应用二进制补丁..."
    
    log_warning "⚠️  这是一个示例补丁过程"
    echo ""
    echo "手动补丁步骤："
    echo "1. 使用十六进制编辑器打开 $DYLIB_PATH"
    echo "2. 找到TimeLock.showAlert方法的机器码"
    echo "3. 将方法开头替换为 ret 指令："
    echo "   ARM64: 0xd65f03c0 (ret)"
    echo "   ARM32: 0xe12fff1e (bx lr)"
    echo "4. 保存文件"
    echo ""
    
    # 提供自动化补丁选项（需要具体地址）
    echo "自动化补丁选项："
    echo "如果您知道确切的函数地址，可以使用以下命令："
    echo ""
    echo "# 示例：在偏移0x1234处写入ret指令"
    echo "# printf '\\xc0\\x03\\x5f\\xd6' | dd of=\"$DYLIB_PATH\" bs=1 seek=\$((0x1234)) count=4 conv=notrunc"
    echo ""
    
    read -p "是否要继续手动补丁过程？(y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "跳过补丁应用"
        return 0
    fi
    
    # 这里可以添加具体的补丁逻辑
    log_warning "请手动完成补丁过程"
    
    return 0
}

# 重新签名
resign_binary() {
    log_info "重新签名二进制文件..."
    
    # 移除现有签名
    if command -v codesign &> /dev/null; then
        codesign --remove-signature "$DYLIB_PATH" 2>/dev/null || true
        
        # 重新签名
        codesign -f -s - "$DYLIB_PATH"
        log_success "重新签名完成"
        
        # 验证签名
        if codesign -v "$DYLIB_PATH" 2>/dev/null; then
            log_success "签名验证通过"
        else
            log_warning "签名验证失败，但可能仍然可用"
        fi
    else
        log_warning "未找到codesign工具，跳过签名"
    fi
    
    return 0
}

# 验证补丁
verify_patch() {
    log_info "验证补丁效果..."
    
    # 检查文件完整性
    if [ ! -f "$DYLIB_PATH" ]; then
        log_error "补丁后文件丢失"
        return 1
    fi
    
    # 比较文件大小
    local original_size=$(stat -f%z "$BACKUP_PATH" 2>/dev/null || stat -c%s "$BACKUP_PATH" 2>/dev/null)
    local patched_size=$(stat -f%z "$DYLIB_PATH" 2>/dev/null || stat -c%s "$DYLIB_PATH" 2>/dev/null)
    
    log_info "原始文件大小: $original_size 字节"
    log_info "补丁后大小: $patched_size 字节"
    
    if [ "$original_size" -eq "$patched_size" ]; then
        log_success "文件大小一致"
    else
        log_warning "文件大小发生变化"
    fi
    
    # 检查是否为有效的Mach-O文件
    if file "$DYLIB_PATH" | grep -q "Mach-O"; then
        log_success "文件格式验证通过"
    else
        log_error "文件格式验证失败"
        return 1
    fi
    
    return 0
}

# 恢复原始文件
restore_original() {
    log_info "恢复原始文件..."
    
    if [ ! -f "$BACKUP_PATH" ]; then
        log_error "未找到备份文件: $BACKUP_PATH"
        return 1
    fi
    
    cp "$BACKUP_PATH" "$DYLIB_PATH"
    log_success "已恢复原始文件"
    
    return 0
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    local temp_files=("symbols_analysis.txt" "strings_analysis.txt")
    for file in "${temp_files[@]}"; do
        if [ -f "$file" ]; then
            rm -f "$file"
            log_info "删除临时文件: $file"
        fi
    done
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项："
    echo "  -h, --help     显示此帮助信息"
    echo "  -b, --backup   仅备份原始文件"
    echo "  -r, --restore  恢复原始文件"
    echo "  -a, --analyze  仅分析目标文件"
    echo "  -c, --cleanup  清理临时文件"
    echo ""
    echo "示例："
    echo "  $0              # 交互式补丁过程"
    echo "  $0 --backup     # 仅备份文件"
    echo "  $0 --restore    # 恢复原始文件"
    echo ""
}

# 主函数
main() {
    # 处理命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--backup)
            show_header
            check_environment && backup_original
            exit $?
            ;;
        -r|--restore)
            show_header
            restore_original
            exit $?
            ;;
        -a|--analyze)
            show_header
            check_environment && analyze_target_functions
            exit $?
            ;;
        -c|--cleanup)
            cleanup
            exit 0
            ;;
    esac
    
    # 开始补丁日志
    echo "TimeLock手动补丁日志 - $(date)" > "$PATCH_LOG"
    
    show_header
    
    # 确认操作
    echo "⚠️  您即将进行手动二进制补丁操作"
    echo "这可能导致应用无法正常运行"
    echo ""
    read -p "确认继续？(y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    
    # 执行补丁流程
    if check_environment && \
       backup_original && \
       analyze_target_functions && \
       find_function_addresses && \
       apply_binary_patch && \
       resign_binary && \
       verify_patch; then
        
        log_success "🎉 手动补丁过程完成"
        echo ""
        echo "📱 请重启Sky-iOS-Gold应用测试效果"
        echo "🔍 如果出现问题，运行 '$0 --restore' 恢复原始文件"
        
    else
        log_error "❌ 补丁过程失败"
        echo ""
        echo "🔧 建议操作："
        echo "1. 检查错误日志: $PATCH_LOG"
        echo "2. 恢复原始文件: $0 --restore"
        echo "3. 尝试使用Substrate Hook方案"
        
        exit 1
    fi
    
    cleanup
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
