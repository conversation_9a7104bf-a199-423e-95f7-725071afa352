#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接验证码绕过工具
直接修改验证逻辑，让验证函数返回成功，外挂插件可直接运行

功能：
- 分析 hhhhsd.dylib 文件
- 找到验证码相关函数
- 直接修改返回值为成功
- 让外挂插件直接运行

版本：1.0
"""

import os
import sys
import struct
import shutil
from pathlib import Path
import subprocess

class DirectVerificationBypass:
    def __init__(self, app_path="."):
        self.app_path = Path(app_path)
        self.dylib_path = self.app_path / "hhhhsd.dylib"
        self.backup_path = self.app_path / "hhhhsd.dylib.backup"
        
        print("🎯 直接验证码绕过工具")
        print(f"📁 工作目录: {self.app_path.absolute()}")
        print(f"🎯 目标文件: {self.dylib_path}")
    
    def backup_original(self):
        """备份原始文件"""
        print("\n💾 备份原始文件...")
        
        if not self.dylib_path.exists():
            print(f"❌ 未找到目标文件: {self.dylib_path}")
            return False
        
        if not self.backup_path.exists():
            shutil.copy2(self.dylib_path, self.backup_path)
            print(f"✅ 已备份: {self.dylib_path.name} -> {self.backup_path.name}")
        else:
            print(f"ℹ️  备份文件已存在: {self.backup_path.name}")
        
        return True
    
    def analyze_dylib(self):
        """分析dylib文件，找到验证相关函数"""
        print("\n🔍 分析dylib文件...")
        
        try:
            # 使用strings命令查找验证相关字符串
            result = subprocess.run(['strings', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            verification_strings = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if any(keyword in line.lower() for keyword in 
                      ['验证', 'verify', 'code', 'auth', 'license', 'activate']):
                    verification_strings.append(line)
            
            print(f"📝 找到 {len(verification_strings)} 个验证相关字符串:")
            for i, s in enumerate(verification_strings[:10]):  # 只显示前10个
                print(f"   {i+1}. {s}")
            
            if len(verification_strings) > 10:
                print(f"   ... 还有 {len(verification_strings) - 10} 个")
            
            return verification_strings
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return []
    
    def find_verification_functions(self):
        """查找验证函数的地址"""
        print("\n🎯 查找验证函数...")
        
        try:
            # 使用nm命令查找符号
            result = subprocess.run(['nm', '-D', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            verification_symbols = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if any(keyword in line.lower() for keyword in 
                      ['timelock', 'alert', 'verify', 'auth']):
                    verification_symbols.append(line)
            
            print(f"🔍 找到 {len(verification_symbols)} 个相关符号:")
            for symbol in verification_symbols[:10]:
                print(f"   {symbol}")
            
            return verification_symbols
            
        except Exception as e:
            print(f"❌ 符号查找失败: {e}")
            return []
    
    def patch_verification_logic(self):
        """直接修改验证逻辑"""
        print("\n🔧 修改验证逻辑...")
        
        # 读取文件内容
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        original_size = len(data)
        patches_applied = 0
        
        # 方法1: 查找并替换验证失败的返回值
        # 在ARM64中，返回false通常是 mov w0, #0; ret
        # 我们将其改为 mov w0, #1; ret (返回true)
        
        # ARM64指令模式
        patterns_to_patch = [
            # mov w0, #0; ret -> mov w0, #1; ret
            (b'\x00\x00\x80\x52\xc0\x03\x5f\xd6', b'\x20\x00\x80\x52\xc0\x03\x5f\xd6'),
            # mov x0, #0; ret -> mov x0, #1; ret  
            (b'\x00\x00\x80\xd2\xc0\x03\x5f\xd6', b'\x20\x00\x80\xd2\xc0\x03\x5f\xd6'),
        ]
        
        for old_pattern, new_pattern in patterns_to_patch:
            offset = 0
            while True:
                pos = data.find(old_pattern, offset)
                if pos == -1:
                    break
                
                # 检查这是否可能是验证函数
                # 简单启发式：检查附近是否有验证相关字符串
                context_start = max(0, pos - 1000)
                context_end = min(len(data), pos + 1000)
                context = data[context_start:context_end]
                
                if any(keyword.encode() in context for keyword in 
                      [b'verify', b'auth', b'code', b'license']):
                    
                    print(f"🎯 在偏移 0x{pos:x} 处找到可能的验证函数")
                    print(f"   原始: {old_pattern.hex()}")
                    print(f"   修改: {new_pattern.hex()}")
                    
                    # 应用补丁
                    data[pos:pos+len(old_pattern)] = new_pattern
                    patches_applied += 1
                
                offset = pos + 1
        
        # 方法2: 查找验证码弹窗相关的字符串并修改
        verification_strings = [
            b'\xe9\xaa\x8c\xe8\xaf\x81',  # "验证" UTF-8
            b'verification',
            b'activate',
            b'license'
        ]
        
        for verify_str in verification_strings:
            pos = data.find(verify_str)
            if pos != -1:
                # 将验证字符串替换为无害字符串
                replacement = b'x' * len(verify_str)
                data[pos:pos+len(verify_str)] = replacement
                patches_applied += 1
                print(f"🔧 替换验证字符串在偏移 0x{pos:x}")
        
        if patches_applied > 0:
            # 保存修改后的文件
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            
            print(f"✅ 应用了 {patches_applied} 个补丁")
            print(f"📊 文件大小: {original_size} -> {len(data)} 字节")
            return True
        else:
            print("⚠️  未找到可修改的验证逻辑")
            return False
    
    def create_verification_success_patch(self):
        """创建验证成功补丁"""
        print("\n🎯 创建验证成功补丁...")
        
        # 读取文件
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        # 查找TimeLock相关的类信息
        timelock_patterns = [
            b'TimeLock',
            b'showAlert',
            b'MyTimer'
        ]
        
        patches_applied = 0
        
        for pattern in timelock_patterns:
            pos = data.find(pattern)
            if pos != -1:
                print(f"🔍 找到 {pattern.decode('utf-8', errors='ignore')} 在偏移 0x{pos:x}")
                
                # 在附近查找可能的验证逻辑
                # 查找附近的函数调用模式
                search_start = max(0, pos - 500)
                search_end = min(len(data), pos + 500)
                
                # 查找返回false的模式并改为返回true
                for i in range(search_start, search_end - 8):
                    # 检查是否是 mov w0, #0; ret 模式
                    if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                        data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                        
                        # 改为 mov w0, #1; ret
                        data[i:i+4] = b'\x20\x00\x80\x52'
                        patches_applied += 1
                        print(f"   🔧 修改返回值在偏移 0x{i:x}")
        
        if patches_applied > 0:
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            print(f"✅ 应用了 {patches_applied} 个验证成功补丁")
            return True
        else:
            print("⚠️  未找到验证返回值模式")
            return False
    
    def patch_plugin_interface(self):
        """修改插件接口，确保外挂插件接收到成功信号"""
        print("\n🔌 修改插件接口...")
        
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        # 查找可能的插件通信接口
        interface_patterns = [
            b'plugin',
            b'callback',
            b'result',
            b'success',
            b'failed'
        ]
        
        patches_applied = 0
        
        for pattern in interface_patterns:
            pos = data.find(pattern)
            if pos != -1:
                print(f"🔍 找到接口相关: {pattern.decode('utf-8', errors='ignore')} 在 0x{pos:x}")
                
                # 在附近查找可能的状态设置
                search_start = max(0, pos - 200)
                search_end = min(len(data), pos + 200)
                
                # 查找设置失败状态的代码并改为成功
                for i in range(search_start, search_end - 4):
                    # 查找可能的状态值设置
                    if data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0
                        data[i:i+4] = b'\x20\x00\x80\x52'   # mov w0, #1
                        patches_applied += 1
                        print(f"   🔧 修改状态值在偏移 0x{i:x}")
        
        if patches_applied > 0:
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            print(f"✅ 修改了 {patches_applied} 个接口状态")
            return True
        else:
            print("ℹ️  未找到需要修改的接口")
            return False
    
    def resign_binary(self):
        """重新签名二进制文件"""
        print("\n✍️  重新签名...")
        
        try:
            # 移除现有签名
            subprocess.run(['codesign', '--remove-signature', str(self.dylib_path)], 
                         check=False, capture_output=True)
            
            # 重新签名
            result = subprocess.run(['codesign', '-f', '-s', '-', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 重新签名成功")
                return True
            else:
                print(f"⚠️  签名警告: {result.stderr}")
                return True  # 即使签名失败也可能可用
                
        except Exception as e:
            print(f"⚠️  签名失败: {e}")
            return True  # 继续执行
    
    def verify_patch(self):
        """验证补丁效果"""
        print("\n🧪 验证补丁效果...")
        
        if not self.dylib_path.exists():
            print("❌ 修改后的文件不存在")
            return False
        
        # 检查文件完整性
        try:
            result = subprocess.run(['file', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if 'Mach-O' in result.stdout:
                print("✅ 文件格式正确")
            else:
                print("⚠️  文件格式可能有问题")
            
            # 比较文件大小
            original_size = self.backup_path.stat().st_size if self.backup_path.exists() else 0
            current_size = self.dylib_path.stat().st_size
            
            print(f"📊 文件大小对比:")
            print(f"   原始: {original_size} 字节")
            print(f"   修改: {current_size} 字节")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def restore_backup(self):
        """恢复备份文件"""
        print("\n🔄 恢复备份文件...")
        
        if self.backup_path.exists():
            shutil.copy2(self.backup_path, self.dylib_path)
            print("✅ 已恢复原始文件")
            return True
        else:
            print("❌ 未找到备份文件")
            return False
    
    def run_bypass(self):
        """执行完整的绕过流程"""
        print("\n🚀 开始直接验证码绕过...")
        
        # 1. 备份原始文件
        if not self.backup_original():
            return False
        
        # 2. 分析文件
        self.analyze_dylib()
        self.find_verification_functions()
        
        # 3. 应用补丁
        success = False
        
        if self.patch_verification_logic():
            success = True
        
        if self.create_verification_success_patch():
            success = True
        
        if self.patch_plugin_interface():
            success = True
        
        if not success:
            print("❌ 未能应用任何补丁")
            return False
        
        # 4. 重新签名
        self.resign_binary()
        
        # 5. 验证结果
        if self.verify_patch():
            print("\n🎉 验证码绕过补丁应用成功！")
            print("\n📱 使用说明:")
            print("1. 重启Sky-iOS-Gold应用")
            print("2. 外挂插件现在应该可以直接运行")
            print("3. 验证码检查将自动返回成功")
            print("4. 如有问题，运行恢复命令")
            return True
        else:
            print("❌ 补丁验证失败")
            return False

def main():
    print("=" * 60)
    print("🎯 直接验证码绕过工具")
    print("让外挂插件直接运行，无需验证码")
    print("=" * 60)
    
    app_path = sys.argv[1] if len(sys.argv) > 1 else "."
    
    try:
        bypass = DirectVerificationBypass(app_path)
        
        print("\n📋 选择操作:")
        print("1. 执行完整绕过流程")
        print("2. 仅分析文件")
        print("3. 恢复原始文件")
        print("0. 退出")
        
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            if bypass.run_bypass():
                print("\n✅ 绕过成功！外挂插件现在可以直接运行")
            else:
                print("\n❌ 绕过失败，请检查错误信息")
        elif choice == "2":
            bypass.analyze_dylib()
            bypass.find_verification_functions()
        elif choice == "3":
            bypass.restore_backup()
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
