# TimeLockBypass Makefile
# 《光遇》验证码弹窗绕过 Tweak 构建配置
# 
# 使用方法：
#   make clean  - 清理构建文件
#   make        - 编译Tweak
#   make install - 安装到设备（需要连接越狱设备）
#   make package - 创建deb包

# 目标平台配置
TARGET := iphone:clang:latest:10.0
INSTALL_TARGET_PROCESSES = Sky-iOS-Gold

# 架构支持
ARCHS = arm64 arm64e

# 包含Theos公共配置
include $(THEOS)/makefiles/common.mk

# Tweak配置
TWEAK_NAME = TimeLockBypass

# 源文件
TimeLockBypass_FILES = TimeLockBypass.x

# 编译标志
TimeLockBypass_CFLAGS = -fobjc-arc -Wno-deprecated-declarations
TimeLockBypass_CCFLAGS = -std=c++11

# 链接的框架
TimeLockBypass_FRAMEWORKS = UIKit Foundation

# 私有框架（如果需要）
# TimeLockBypass_PRIVATE_FRAMEWORKS = 

# 库依赖
# TimeLockBypass_LIBRARIES = 

# 安装路径
TimeLockBypass_INSTALL_PATH = /Library/MobileSubstrate/DynamicLibraries

# 包信息
PACKAGE_VERSION = 1.0.0
PACKAGE_NAME = com.timelock.bypass

# 包含Tweak构建规则
include $(THEOS)/makefiles/tweak.mk

# 自定义目标
.PHONY: info backup restore test

# 显示构建信息
info:
	@echo "=== TimeLockBypass 构建信息 ==="
	@echo "目标应用: Sky-iOS-Gold"
	@echo "支持架构: $(ARCHS)"
	@echo "安装路径: $(TimeLockBypass_INSTALL_PATH)"
	@echo "包版本: $(PACKAGE_VERSION)"
	@echo ""
	@echo "可用命令:"
	@echo "  make clean   - 清理构建文件"
	@echo "  make         - 编译Tweak"
	@echo "  make install - 安装到设备"
	@echo "  make package - 创建deb包"
	@echo "  make backup  - 备份原始dylib"
	@echo "  make restore - 恢复原始dylib"
	@echo "  make test    - 运行测试脚本"

# 备份原始文件
backup:
	@echo "=== 备份原始文件 ==="
	@if [ -f "hhhhsd.dylib" ]; then \
		cp hhhhsd.dylib hhhhsd.dylib.backup; \
		echo "✅ 已备份 hhhhsd.dylib -> hhhhsd.dylib.backup"; \
	else \
		echo "⚠️  未找到 hhhhsd.dylib 文件"; \
	fi

# 恢复原始文件
restore:
	@echo "=== 恢复原始文件 ==="
	@if [ -f "hhhhsd.dylib.backup" ]; then \
		cp hhhhsd.dylib.backup hhhhsd.dylib; \
		echo "✅ 已恢复 hhhhsd.dylib.backup -> hhhhsd.dylib"; \
	else \
		echo "⚠️  未找到备份文件 hhhhsd.dylib.backup"; \
	fi

# 运行测试脚本
test:
	@echo "=== 运行测试脚本 ==="
	@if [ -f "test_bypass.sh" ]; then \
		chmod +x test_bypass.sh; \
		./test_bypass.sh; \
	else \
		echo "⚠️  未找到测试脚本 test_bypass.sh"; \
	fi

# 安装后的清理工作
after-install::
	@echo "=== 安装完成 ==="
	@echo "✅ TimeLockBypass 已安装到设备"
	@echo "📱 请重启 Sky-iOS-Gold 应用以生效"
	@echo ""
	@echo "🔍 查看日志命令:"
	@echo "   idevicesyslog | grep TimeLockBypass"
	@echo ""
	@echo "⚠️  如果遇到问题，请运行 'make restore' 恢复原始文件"

# 打包前的准备工作
before-package::
	@echo "=== 准备打包 ==="
	@echo "📦 正在创建 TimeLockBypass deb 包..."

# 打包后的信息
after-package::
	@echo "=== 打包完成 ==="
	@echo "✅ deb 包已创建在 packages/ 目录"
	@echo "📱 可以使用 Cydia/Sileo 安装此包"

# 清理扩展
clean::
	@echo "🧹 清理构建文件..."
	@rm -rf .theos obj packages

# 帮助信息
help: info
