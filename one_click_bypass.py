#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键验证码绕过工具
直接修改验证逻辑，让外挂插件无需验证码即可运行

功能：
- 一键完成所有绕过操作
- 修改验证函数返回成功
- 确保外挂插件直接可用
- 自动备份和恢复

版本：1.0
"""

import os
import sys
import shutil
from pathlib import Path
import subprocess

class OneClickBypass:
    def __init__(self, app_path="."):
        self.app_path = Path(app_path)
        self.dylib_path = self.app_path / "hhhhsd.dylib"
        self.backup_path = self.app_path / "hhhhsd.dylib.original"
        
        print("🎯 一键验证码绕过工具")
        print("让外挂插件直接运行，无需验证码！")
        print("=" * 50)
    
    def check_files(self):
        """检查必要文件"""
        print("🔍 检查文件...")
        
        if not self.dylib_path.exists():
            print(f"❌ 未找到目标文件: {self.dylib_path}")
            return False
        
        print(f"✅ 找到目标文件: {self.dylib_path}")
        
        file_size = self.dylib_path.stat().st_size
        print(f"📊 文件大小: {file_size:,} 字节")
        
        return True
    
    def backup_original(self):
        """备份原始文件"""
        print("\n💾 备份原始文件...")
        
        if not self.backup_path.exists():
            shutil.copy2(self.dylib_path, self.backup_path)
            print(f"✅ 备份完成: {self.backup_path.name}")
        else:
            print(f"ℹ️  备份已存在: {self.backup_path.name}")
        
        return True
    
    def apply_bypass_patches(self):
        """应用绕过补丁"""
        print("\n🔧 应用验证码绕过补丁...")
        
        # 读取文件
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        patches_applied = 0
        
        # 1. 修改验证函数返回值
        print("   🎯 修改验证函数返回值...")
        
        # ARM64: mov w0, #0; ret -> mov w0, #1; ret
        old_pattern = b'\x00\x00\x80\x52\xc0\x03\x5f\xd6'  # 返回false
        new_pattern = b'\x20\x00\x80\x52\xc0\x03\x5f\xd6'  # 返回true
        
        offset = 0
        while True:
            pos = data.find(old_pattern, offset)
            if pos == -1:
                break
            
            # 检查上下文是否与验证相关
            context_start = max(0, pos - 500)
            context_end = min(len(data), pos + 500)
            context = data[context_start:context_end]
            
            verification_keywords = [
                b'verify', b'auth', b'check', b'license', b'activate',
                b'TimeLock', b'showAlert', b'code'
            ]
            
            if any(keyword in context for keyword in verification_keywords):
                data[pos:pos+len(old_pattern)] = new_pattern
                patches_applied += 1
                print(f"     ✅ 修改验证返回值在 0x{pos:x}")
            
            offset = pos + 1
        
        # 2. 修改验证字符串
        print("   🔧 修改验证字符串...")
        
        string_replacements = [
            (b'\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81', b'xxxxxxxxx'),  # "验证码"
            (b'\xe9\xaa\x8c\xe8\xaf\x81', b'xxxx'),                    # "验证"
            (b'verification', b'xxxxxxxxxxx'),
            (b'activate', b'xxxxxxxx'),
            (b'license', b'xxxxxxx'),
        ]
        
        for old_str, new_str in string_replacements:
            count = data.count(old_str)
            if count > 0:
                data = data.replace(old_str, new_str)
                patches_applied += count
                print(f"     ✅ 替换字符串: {old_str} ({count}次)")
        
        # 3. 专门处理TimeLock类
        print("   🎯 处理TimeLock类...")
        
        timelock_pos = data.find(b'TimeLock')
        if timelock_pos != -1:
            print(f"     🔍 找到TimeLock类在 0x{timelock_pos:x}")
            
            # 在TimeLock附近查找showAlert函数
            search_start = max(0, timelock_pos - 2000)
            search_end = min(len(data), timelock_pos + 2000)
            
            for i in range(search_start, search_end - 8):
                # 查找 mov w0, #0; ret 模式
                if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                    data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                    
                    data[i:i+4] = b'\x20\x00\x80\x52'  # 改为返回true
                    patches_applied += 1
                    print(f"     ✅ 修改TimeLock返回值在 0x{i:x}")
        
        # 4. 确保插件接收成功信号
        print("   📡 确保插件接收成功信号...")
        
        plugin_keywords = [b'plugin', b'callback', b'result', b'status']
        
        for keyword in plugin_keywords:
            pos = data.find(keyword)
            if pos != -1:
                # 在附近查找状态设置
                search_start = max(0, pos - 300)
                search_end = min(len(data), pos + 300)
                
                for i in range(search_start, search_end - 4):
                    if data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0
                        data[i:i+4] = b'\x20\x00\x80\x52'   # mov w0, #1
                        patches_applied += 1
                        print(f"     ✅ 修改插件状态在 0x{i:x}")
        
        # 保存修改后的文件
        if patches_applied > 0:
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            
            print(f"\n✅ 总共应用了 {patches_applied} 个补丁")
            return True
        else:
            print("\n❌ 未找到可修改的验证逻辑")
            return False
    
    def resign_file(self):
        """重新签名文件"""
        print("\n✍️  重新签名文件...")
        
        try:
            # 移除现有签名
            subprocess.run(['codesign', '--remove-signature', str(self.dylib_path)], 
                         check=False, capture_output=True)
            
            # 重新签名
            result = subprocess.run(['codesign', '-f', '-s', '-', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 重新签名成功")
            else:
                print("⚠️  签名可能有问题，但文件仍可使用")
            
            return True
            
        except Exception as e:
            print(f"⚠️  签名失败: {e}")
            return True  # 即使签名失败也继续
    
    def verify_patch(self):
        """验证补丁效果"""
        print("\n🧪 验证补丁效果...")
        
        try:
            # 检查文件完整性
            result = subprocess.run(['file', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if 'Mach-O' in result.stdout:
                print("✅ 文件格式正确")
            else:
                print("⚠️  文件格式可能有问题")
            
            # 检查文件大小
            current_size = self.dylib_path.stat().st_size
            original_size = self.backup_path.stat().st_size
            
            print(f"📊 文件大小对比:")
            print(f"   原始: {original_size:,} 字节")
            print(f"   修改: {current_size:,} 字节")
            
            if current_size == original_size:
                print("✅ 文件大小一致")
            else:
                print("⚠️  文件大小发生变化")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def show_success_message(self):
        """显示成功信息"""
        print("\n" + "=" * 50)
        print("🎉 验证码绕过成功！")
        print("=" * 50)
        print()
        print("✅ 验证码检查已被绕过")
        print("✅ 外挂插件可以直接运行")
        print("✅ 无需输入任何验证码")
        print("✅ 所有功能完全可用")
        print()
        print("📱 使用说明:")
        print("1. 重启Sky-iOS-Gold应用")
        print("2. 直接使用外挂功能")
        print("3. 验证码会自动通过")
        print("4. 享受无限制体验！")
        print()
        print("🔄 如需恢复原始文件:")
        print(f"   cp {self.backup_path.name} {self.dylib_path.name}")
        print()
    
    def restore_original(self):
        """恢复原始文件"""
        print("\n🔄 恢复原始文件...")
        
        if self.backup_path.exists():
            shutil.copy2(self.backup_path, self.dylib_path)
            print("✅ 已恢复原始文件")
            print("📱 请重启应用以生效")
            return True
        else:
            print("❌ 未找到备份文件")
            return False
    
    def run_one_click_bypass(self):
        """执行一键绕过"""
        print("🚀 开始一键验证码绕过...")
        print()
        
        # 执行所有步骤
        if (self.check_files() and
            self.backup_original() and
            self.apply_bypass_patches() and
            self.resign_file() and
            self.verify_patch()):
            
            self.show_success_message()
            return True
        else:
            print("\n❌ 绕过过程失败")
            print("🔄 建议恢复原始文件")
            return False

def main():
    print("=" * 60)
    print("🎯 一键验证码绕过工具")
    print("让外挂插件直接运行，无需验证码！")
    print("=" * 60)
    
    app_path = sys.argv[1] if len(sys.argv) > 1 else "."
    
    try:
        bypass = OneClickBypass(app_path)
        
        print("\n📋 选择操作:")
        print("1. 🚀 一键绕过验证码（推荐）")
        print("2. 🔄 恢复原始文件")
        print("3. 📊 显示文件状态")
        print("0. 👋 退出")
        
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            print("\n⚠️  即将修改验证逻辑")
            print("修改后外挂插件将直接运行，无需验证码")
            print("原始文件将被自动备份")
            print()
            confirm = input("确认开始一键绕过？(y/N): ").strip().lower()
            
            if confirm == 'y':
                bypass.run_one_click_bypass()
            else:
                print("❌ 用户取消操作")
                
        elif choice == "2":
            bypass.restore_original()
            
        elif choice == "3":
            if bypass.dylib_path.exists():
                size = bypass.dylib_path.stat().st_size
                print(f"\n📁 目标文件: {bypass.dylib_path}")
                print(f"📊 文件大小: {size:,} 字节")
                
                if bypass.backup_path.exists():
                    backup_size = bypass.backup_path.stat().st_size
                    print(f"💾 备份文件: {bypass.backup_path} ({backup_size:,} 字节)")
                else:
                    print("💾 备份文件: 不存在")
            else:
                print(f"\n❌ 目标文件不存在: {bypass.dylib_path}")
                
        elif choice == "0":
            print("\n👋 退出程序")
        else:
            print("\n❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
