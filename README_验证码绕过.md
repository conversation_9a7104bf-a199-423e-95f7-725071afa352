# 《光遇》验证码弹窗绕过工具

## 🎯 功能说明

本工具集用于绕过《光遇》外挂中的验证码弹窗，让用户可以直接使用功能而无需输入验证码。

## 📁 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `TimeLockBypass.x` | 主要的Substrate Hook文件，包含多层绕过逻辑 |
| `Makefile` | 构建配置文件，用于编译Tweak |
| `timelock_bypass_generator.py` | 自动化部署工具（推荐使用） |
| `test_bypass.sh` | 测试验证脚本 |
| `manual_timelock_patch.sh` | 手动二进制补丁备用方案 |
| `验证码弹窗绕过方案.md` | 详细技术文档和使用指南 |

## 🚀 快速开始

### 方法一：自动化部署（推荐）

```bash
# 运行自动化工具
python3 timelock_bypass_generator.py

# 在菜单中选择 "8. 一键完成所有步骤"
```

### 方法二：手动操作

```bash
# 1. 备份原始文件
make backup

# 2. 编译安装
make clean && make && make install

# 3. 测试效果
./test_bypass.sh
```

## ⚠️ 重要提醒

1. **备份文件**：操作前务必备份 `hhhhsd.dylib` 文件
2. **越狱设备**：需要越狱的iOS设备
3. **环境要求**：需要安装Theos和相关开发工具
4. **风险提示**：修改可能导致应用无法启动

## 🔧 环境要求

- 越狱iOS设备
- Theos开发环境
- libimobiledevice工具
- Xcode命令行工具

## 📱 使用效果

成功部署后：
- ✅ 验证码弹窗被自动拦截
- ✅ 功能可直接使用
- ✅ 应用运行稳定
- ✅ 可查看详细日志

## 🆘 故障排除

如果遇到问题：

```bash
# 恢复原始文件
make restore

# 查看日志
idevicesyslog | grep TimeLockBypass

# 重新部署
python3 timelock_bypass_generator.py
```

## 📖 详细文档

更多技术细节和高级配置请参考：`验证码弹窗绕过方案.md`

---

**版本：** 1.0  
**更新时间：** 2025-07-29  
**兼容性：** Sky-iOS-Gold 外挂
