# 《光遇》外挂验证码弹窗绕过技术分析

## 🔍 验证码机制分析

### 发现的关键信息
通过对`hhhhsd.dylib`的分析，我们发现了验证码弹窗的实现机制：

#### 核心类和方法
```swift
// 在 DDDD/TimeLock.swift 中发现
class TimeLock {
    func showAlert(withMessage: String)
    func showAlert2(withMessage: String)
    var timer: NSTimer?
    var myTimer: MyTimer?
}

class MyTimer {
    // 定时器相关逻辑
}
```

#### 关键符号分析
```bash
# 从符号表中发现的关键函数
_$s4DDDD8TimeLock9showAlert11withMessageySS_tF
_$s4DDDD8TimeLock10showAlert211withMessageySS_tF
_TtC4DDDD8TimeLock
_TtC4DDDD7MyTimer
```

## 🛠️ 绕过方案

### 方案1: Hook弹窗显示方法（推荐）

#### 1.1 使用Substrate Hook
```objc
// TimeLockBypass.x
%hook TimeLock

// Hook showAlert方法，直接返回不显示弹窗
- (void)showAlertWithMessage:(NSString *)message {
    NSLog(@"[TimeLockBypass] 拦截验证码弹窗: %@", message);
    // 不调用 %orig，直接返回，阻止弹窗显示
    return;
}

// Hook showAlert2方法
- (void)showAlert2WithMessage:(NSString *)message {
    NSLog(@"[TimeLockBypass] 拦截验证码弹窗2: %@", message);
    return;
}

%end
```

#### 1.2 编译和注入
```makefile
# Makefile
TARGET := iphone:clang:latest:10.0
INSTALL_TARGET_PROCESSES = Sky-iOS-Gold

include $(THEOS)/makefiles/common.mk

TWEAK_NAME = TimeLockBypass
TimeLockBypass_FILES = TimeLockBypass.x
TimeLockBypass_CFLAGS = -fobjc-arc

include $(THEOS)/makefiles/tweak.mk
```

### 方案2: Hook UIAlertController（通用方案）

#### 2.1 拦截所有弹窗
```objc
// UniversalAlertBypass.x
%hook UIAlertController

+ (instancetype)alertControllerWithTitle:(NSString *)title 
                                 message:(NSString *)message 
                          preferredStyle:(UIAlertControllerStyle)preferredStyle {
    
    // 检查是否为验证码相关弹窗
    if ([title containsString:@"验证"] || 
        [message containsString:@"验证"] ||
        [title containsString:@"code"] ||
        [message containsString:@"verification"]) {
        
        NSLog(@"[AlertBypass] 拦截验证码弹窗 - 标题: %@, 消息: %@", title, message);
        
        // 返回一个空的AlertController，不会显示
        return nil;
    }
    
    // 其他弹窗正常显示
    return %orig(title, message, preferredStyle);
}

%end
```

#### 2.2 Hook present方法
```objc
%hook UIViewController

- (void)presentViewController:(UIViewController *)viewControllerToPresent 
                     animated:(BOOL)flag 
                   completion:(void (^)(void))completion {
    
    // 检查是否为UIAlertController
    if ([viewControllerToPresent isKindOfClass:[UIAlertController class]]) {
        UIAlertController *alert = (UIAlertController *)viewControllerToPresent;
        
        // 检查弹窗内容
        if ([alert.title containsString:@"验证"] || 
            [alert.message containsString:@"验证"]) {
            
            NSLog(@"[PresentBypass] 阻止验证码弹窗显示");
            
            // 直接调用completion，不显示弹窗
            if (completion) {
                completion();
            }
            return;
        }
    }
    
    // 其他视图控制器正常显示
    %orig(viewControllerToPresent, flag, completion);
}

%end
```

### 方案3: 修改定时器逻辑

#### 3.1 Hook定时器创建
```objc
%hook NSTimer

+ (NSTimer *)scheduledTimerWithTimeInterval:(NSTimeInterval)ti 
                                     target:(id)aTarget 
                                   selector:(SEL)aSelector 
                                   userInfo:(id)userInfo 
                                    repeats:(BOOL)yesOrNo {
    
    // 检查是否为TimeLock相关的定时器
    if ([aTarget isKindOfClass:NSClassFromString(@"TimeLock")] ||
        [aTarget isKindOfClass:NSClassFromString(@"MyTimer")]) {
        
        NSLog(@"[TimerBypass] 拦截TimeLock定时器创建");
        
        // 返回一个无效的定时器
        return nil;
    }
    
    return %orig(ti, aTarget, aSelector, userInfo, yesOrNo);
}

%end
```

### 方案4: 内存补丁（高级方案）

#### 4.1 直接修改函数实现
```c
#include <mach/mach.h>
#include <mach/vm_map.h>

void patch_timelock_function() {
    // 获取TimeLock类的showAlert方法地址
    Class timeLockClass = NSClassFromString(@"TimeLock");
    Method showAlertMethod = class_getInstanceMethod(timeLockClass, 
                                                    @selector(showAlertWithMessage:));
    
    if (showAlertMethod) {
        IMP originalIMP = method_getImplementation(showAlertMethod);
        
        // 修改内存保护
        vm_protect(mach_task_self(), (vm_address_t)originalIMP, sizeof(void*), 
                   FALSE, VM_PROT_READ | VM_PROT_WRITE | VM_PROT_EXECUTE);
        
        // 创建空函数实现（直接返回）
        // ARM64: ret指令 = 0xd65f03c0
        uint32_t ret_instruction = 0xd65f03c0;
        *(uint32_t*)originalIMP = ret_instruction;
        
        // 清除指令缓存
        sys_icache_invalidate((void*)originalIMP, sizeof(uint32_t));
        
        // 恢复内存保护
        vm_protect(mach_task_self(), (vm_address_t)originalIMP, sizeof(void*), 
                   FALSE, VM_PROT_READ | VM_PROT_EXECUTE);
        
        NSLog(@"[MemoryPatch] TimeLock.showAlert 已被补丁");
    }
}
```

## 🔧 实用工具脚本

### 自动化绕过工具
```python
#!/usr/bin/env python3
# timelock_bypass_generator.py

import os
import subprocess
from pathlib import Path

class TimeLockBypassGenerator:
    def __init__(self, app_path):
        self.app_path = Path(app_path)
        self.tweak_name = "TimeLockBypass"
    
    def generate_bypass_tweak(self):
        """生成绕过Tweak代码"""
        tweak_code = '''
%hook TimeLock

- (void)showAlertWithMessage:(NSString *)message {
    NSLog(@"[TimeLockBypass] 验证码弹窗已拦截: %@", message);
    return; // 直接返回，不显示弹窗
}

- (void)showAlert2WithMessage:(NSString *)message {
    NSLog(@"[TimeLockBypass] 验证码弹窗2已拦截: %@", message);
    return;
}

%end

%hook UIAlertController

+ (instancetype)alertControllerWithTitle:(NSString *)title 
                                 message:(NSString *)message 
                          preferredStyle:(UIAlertControllerStyle)preferredStyle {
    
    // 检查验证码相关关键词
    NSArray *keywords = @[@"验证", @"code", @"verification", @"auth", @"密码"];
    
    for (NSString *keyword in keywords) {
        if ([title containsString:keyword] || [message containsString:keyword]) {
            NSLog(@"[TimeLockBypass] 通用弹窗拦截 - %@: %@", title, message);
            return nil;
        }
    }
    
    return %orig(title, message, preferredStyle);
}

%end

%ctor {
    NSLog(@"[TimeLockBypass] 验证码绕过模块已加载");
}
'''
        
        # 保存Tweak文件
        with open(f"{self.tweak_name}.x", "w") as f:
            f.write(tweak_code)
        
        # 生成Makefile
        makefile_content = f'''
TARGET := iphone:clang:latest:10.0
INSTALL_TARGET_PROCESSES = Sky-iOS-Gold

include $(THEOS)/makefiles/common.mk

TWEAK_NAME = {self.tweak_name}
{self.tweak_name}_FILES = {self.tweak_name}.x
{self.tweak_name}_CFLAGS = -fobjc-arc

include $(THEOS)/makefiles/tweak.mk
'''
        
        with open("Makefile", "w") as f:
            f.write(makefile_content)
        
        print(f"✅ 绕过Tweak代码已生成: {self.tweak_name}.x")
    
    def compile_and_install(self):
        """编译并安装Tweak"""
        try:
            # 编译
            subprocess.run(["make", "clean"], check=True)
            subprocess.run(["make"], check=True)
            
            # 安装到设备
            subprocess.run(["make", "install"], check=True)
            
            print("✅ Tweak编译安装成功")
            print("📱 请重启目标应用以生效")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 编译失败: {e}")
    
    def generate_manual_patch(self):
        """生成手动补丁脚本"""
        patch_script = '''#!/bin/bash
# manual_timelock_patch.sh

APP_PATH="Sky-iOS-Gold.app"
DYLIB_PATH="$APP_PATH/hhhhsd.dylib"

echo "=== TimeLock验证码绕过补丁 ==="

# 备份原文件
cp "$DYLIB_PATH" "$DYLIB_PATH.backup"
echo "✅ 已备份原文件"

# 使用十六进制编辑器修改关键函数
# 这里需要根据具体的函数地址进行修改
echo "⚠️  需要手动使用十六进制编辑器修改以下地址:"
echo "   - TimeLock.showAlert: 替换为 ret 指令 (0xd65f03c0)"
echo "   - TimeLock.showAlert2: 替换为 ret 指令 (0xd65f03c0)"

# 重新签名
codesign -f -s - "$APP_PATH"
echo "✅ 重新签名完成"

echo "🎉 补丁应用完成，请重启应用测试"
'''
        
        with open("manual_timelock_patch.sh", "w") as f:
            f.write(patch_script)
        
        os.chmod("manual_timelock_patch.sh", 0o755)
        print("✅ 手动补丁脚本已生成: manual_timelock_patch.sh")

# 使用示例
if __name__ == "__main__":
    generator = TimeLockBypassGenerator("Sky-iOS-Gold.app")
    generator.generate_bypass_tweak()
    generator.generate_manual_patch()
    
    # 如果环境支持，可以直接编译安装
    # generator.compile_and_install()
```

## 📋 测试验证

### 验证绕过效果
```bash
#!/bin/bash
# test_bypass.sh

echo "=== 验证码绕过测试 ==="

# 1. 检查Tweak是否正确加载
echo "1. 检查日志输出..."
idevicesyslog | grep "TimeLockBypass"

# 2. 启动应用并监控
echo "2. 启动应用进行测试..."
# 这里需要手动启动应用

# 3. 检查是否还有弹窗
echo "3. 如果没有验证码弹窗出现，说明绕过成功"
```

## ⚠️ 注意事项

### 风险提示
1. **检测风险**: 应用可能有反Hook检测机制
2. **更新风险**: 应用更新可能修改验证逻辑
3. **稳定性**: 过度Hook可能导致应用崩溃

### 建议措施
1. **备份原文件**: 修改前务必备份
2. **测试环境**: 在隔离环境中测试
3. **逐步验证**: 先测试单个方法的Hook效果

## 🚀 实际部署指南

### 快速开始

项目中已包含完整的绕过实现，包括以下文件：

- `TimeLockBypass.x` - 主要的Substrate Hook文件
- `Makefile` - 构建配置文件
- `timelock_bypass_generator.py` - 自动化部署工具
- `test_bypass.sh` - 测试验证脚本
- `manual_timelock_patch.sh` - 手动补丁备用方案

### 方法一：自动化部署（推荐）

```bash
# 1. 运行自动化工具
python3 timelock_bypass_generator.py

# 2. 选择"8. 一键完成所有步骤"
# 工具会自动完成：
# - 环境检查
# - 文件备份
# - 编译Tweak
# - 安装到设备
# - 测试验证
```

### 方法二：手动编译安装

```bash
# 1. 备份原始文件
make backup

# 2. 编译Tweak
make clean
make

# 3. 安装到设备
make install

# 4. 测试效果
./test_bypass.sh
```

### 方法三：手动二进制补丁

```bash
# 仅在Substrate方案失效时使用
./manual_timelock_patch.sh
```

## 📋 使用说明

### 环境要求

1. **越狱iOS设备**
2. **Theos开发环境**
   ```bash
   # 安装Theos
   bash -c "$(curl -fsSL https://raw.githubusercontent.com/theos/theos/master/bin/install-theos)"
   ```
3. **libimobiledevice工具**
   ```bash
   # macOS
   brew install libimobiledevice

   # Ubuntu/Debian
   sudo apt install libimobiledevice-utils
   ```

### 详细步骤

#### 1. 环境准备
```bash
# 检查Theos环境
echo $THEOS

# 检查设备连接
idevice_id -l

# 检查必要工具
which make clang ldid
```

#### 2. 编译安装
```bash
# 进入项目目录
cd /path/to/Sky-iOS-Gold.app

# 备份原始文件（重要！）
make backup

# 编译
make clean && make

# 安装到设备
make install
```

#### 3. 验证效果
```bash
# 运行测试脚本
chmod +x test_bypass.sh
./test_bypass.sh

# 或手动查看日志
idevicesyslog | grep TimeLockBypass
```

#### 4. 故障排除
```bash
# 如果出现问题，恢复原始文件
make restore

# 查看详细日志
idevicesyslog | grep -E "TimeLockBypass|BYPASS"

# 重新编译安装
make clean && make && make install
```

## 🔧 高级配置

### 自定义关键词
编辑 `TimeLockBypass.x` 文件中的 `verificationKeywords` 数组：

```objc
verificationKeywords = @[
    @"验证", @"code", @"verification", @"auth", @"密码",
    @"激活", @"activate", @"license", @"key", @"unlock",
    @"注册", @"register", @"login", @"登录",
    // 添加自定义关键词
    @"custom_keyword"
];
```

### 调试模式
在 `TimeLockBypass.x` 中启用详细日志：

```objc
// 修改日志宏定义
#define TLBLog(fmt, ...) NSLog(@"[TimeLockBypass][DEBUG] " fmt, ##__VA_ARGS__)
```

## 🎯 总结

通过分析发现，验证码弹窗主要由`hhhhsd.dylib`中的`TimeLock`类实现。已实现的绕过方案包括：

1. **Hook TimeLock类的showAlert方法**（最直接）
2. **Hook UIAlertController创建**（最通用）
3. **Hook UIViewController的present方法**（最保险）
4. **Hook NSTimer定时器**（防止定时弹窗）

**推荐使用顺序：**
1. 优先使用自动化部署工具
2. 如果失败，尝试手动编译安装
3. 最后考虑手动二进制补丁

**成功标志：**
- 应用启动时日志显示 `[TimeLockBypass] 验证码绕过模块已加载`
- 触发验证码时显示拦截日志
- 功能正常使用且无弹窗干扰
