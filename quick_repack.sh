#!/bin/bash
# quick_repack.sh
# 快速IPA重新打包脚本
# 
# 功能：快速将修改后的应用重新打包成IPA
# 使用：./quick_repack.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
APP_NAME="Sky-iOS-Gold"
APP_PATH="."
OUTPUT_DIR="../Modified_IPA"
IPA_NAME="${APP_NAME}_Bypassed.ipa"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_header() {
    echo "=================================================="
    echo "📦 快速IPA重新打包工具"
    echo "=================================================="
    echo ""
}

check_environment() {
    log_info "检查环境..."
    
    # 检查必要文件
    if [ ! -d "$APP_PATH" ]; then
        log_error "应用目录不存在: $APP_PATH"
        exit 1
    fi
    
    if [ ! -f "$APP_PATH/$APP_NAME" ]; then
        log_error "应用主程序不存在: $APP_PATH/$APP_NAME"
        exit 1
    fi
    
    if [ ! -f "$APP_PATH/Info.plist" ]; then
        log_error "Info.plist不存在"
        exit 1
    fi
    
    if [ ! -f "$APP_PATH/hhhhsd.dylib" ]; then
        log_error "hhhhsd.dylib不存在"
        exit 1
    fi
    
    log_success "环境检查通过"
}

backup_original() {
    log_info "备份原始应用..."
    
    local backup_dir="../${APP_NAME}_Original_Backup"
    
    if [ ! -d "$backup_dir" ]; then
        cp -R "$APP_PATH" "$backup_dir"
        log_success "已备份到: $backup_dir"
    else
        log_warning "备份已存在: $backup_dir"
    fi
}

apply_quick_bypass() {
    log_info "应用快速绕过补丁..."
    
    # 使用Python工具应用补丁
    if [ -f "precise_bypass.py" ]; then
        log_info "使用精确绕过工具..."
        python3 precise_bypass.py << EOF
1
y
EOF
        if [ $? -eq 0 ]; then
            log_success "精确绕过补丁应用成功"
        else
            log_warning "精确绕过失败，尝试一键绕过..."
            python3 one_click_bypass.py << EOF
1
y
EOF
        fi
    elif [ -f "one_click_bypass.py" ]; then
        log_info "使用一键绕过工具..."
        python3 one_click_bypass.py << EOF
1
y
EOF
    else
        log_error "未找到绕过工具"
        exit 1
    fi
}

modify_app_info() {
    log_info "修改应用信息..."
    
    # 修改Info.plist中的应用名称
    if command -v plutil &> /dev/null; then
        # 获取当前应用名称
        local current_name=$(plutil -extract CFBundleDisplayName raw "$APP_PATH/Info.plist" 2>/dev/null || echo "$APP_NAME")
        
        # 修改为绕过版本
        plutil -replace CFBundleDisplayName -string "${current_name} Bypassed" "$APP_PATH/Info.plist"
        
        # 修改版本号
        local current_version=$(plutil -extract CFBundleShortVersionString raw "$APP_PATH/Info.plist" 2>/dev/null || echo "1.0")
        plutil -replace CFBundleShortVersionString -string "${current_version}.bypassed" "$APP_PATH/Info.plist"
        
        log_success "应用信息修改完成"
    else
        log_warning "plutil不可用，跳过应用信息修改"
    fi
}

resign_app() {
    log_info "重新签名应用..."
    
    # 移除现有签名
    codesign --remove-signature "$APP_PATH/hhhhsd.dylib" 2>/dev/null || true
    codesign --remove-signature "$APP_PATH/$APP_NAME" 2>/dev/null || true
    codesign --remove-signature "$APP_PATH" 2>/dev/null || true
    
    # 重新签名
    codesign -f -s - "$APP_PATH/hhhhsd.dylib" 2>/dev/null || log_warning "dylib签名失败"
    codesign -f -s - "$APP_PATH/$APP_NAME" 2>/dev/null || log_warning "主程序签名失败"
    codesign -f -s - "$APP_PATH" 2>/dev/null || log_warning "应用签名失败"
    
    log_success "重新签名完成"
}

create_ipa() {
    log_info "创建IPA包..."
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 创建临时Payload目录
    local temp_dir="$OUTPUT_DIR/temp_payload"
    local payload_dir="$temp_dir/Payload"
    
    rm -rf "$temp_dir"
    mkdir -p "$payload_dir"
    
    # 复制应用到Payload目录
    cp -R "$APP_PATH" "$payload_dir/${APP_NAME}.app"
    
    # 添加绕过信息文件
    cat > "$payload_dir/${APP_NAME}.app/bypass_info.txt" << EOF
验证码永久绕过版本
修改时间: $(date)
外挂功能: 永久启用
验证码: 永久绕过
安装方法: 见IPA安装指南.md
EOF
    
    # 创建IPA文件
    local ipa_path="$OUTPUT_DIR/$IPA_NAME"
    rm -f "$ipa_path"
    
    cd "$temp_dir"
    zip -r "../$IPA_NAME" Payload/ >/dev/null 2>&1
    cd - >/dev/null
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    if [ -f "$ipa_path" ]; then
        local ipa_size=$(stat -f%z "$ipa_path" 2>/dev/null || stat -c%s "$ipa_path" 2>/dev/null)
        log_success "IPA包创建成功: $ipa_path"
        log_info "IPA大小: $(echo $ipa_size | awk '{printf "%.1f MB", $1/1024/1024}')"
    else
        log_error "IPA包创建失败"
        exit 1
    fi
}

verify_ipa() {
    log_info "验证IPA包..."
    
    local ipa_path="$OUTPUT_DIR/$IPA_NAME"
    
    # 检查文件存在
    if [ ! -f "$ipa_path" ]; then
        log_error "IPA文件不存在"
        return 1
    fi
    
    # 检查ZIP结构
    if unzip -t "$ipa_path" >/dev/null 2>&1; then
        log_success "IPA文件结构正确"
    else
        log_error "IPA文件结构损坏"
        return 1
    fi
    
    # 检查必要文件
    if unzip -l "$ipa_path" | grep -q "Payload/${APP_NAME}.app/${APP_NAME}"; then
        log_success "包含主程序文件"
    else
        log_error "缺少主程序文件"
        return 1
    fi
    
    if unzip -l "$ipa_path" | grep -q "Payload/${APP_NAME}.app/hhhhsd.dylib"; then
        log_success "包含外挂文件"
    else
        log_error "缺少外挂文件"
        return 1
    fi
    
    log_success "IPA验证通过"
    return 0
}

show_completion_info() {
    echo ""
    echo "=================================================="
    echo "🎉 IPA重新打包完成！"
    echo "=================================================="
    echo ""
    echo "📁 输出文件: $OUTPUT_DIR/$IPA_NAME"
    echo "📊 文件大小: $(ls -lh "$OUTPUT_DIR/$IPA_NAME" | awk '{print $5}')"
    echo ""
    echo "📱 安装方法:"
    echo "1. 使用AltStore/Sideloadly安装"
    echo "2. 使用3uTools等工具安装"
    echo "3. 参考 IPA安装指南.md"
    echo ""
    echo "✨ 修改效果:"
    echo "✅ 验证码永久绕过"
    echo "✅ 外挂功能直接启用"
    echo "✅ 无需任何验证操作"
    echo ""
    echo "⚠️  安装前请卸载原版应用"
    echo ""
}

main() {
    show_header
    
    # 确认操作
    echo "⚠️  即将执行快速IPA重新打包"
    echo "这将修改应用并创建新的IPA文件"
    echo ""
    read -p "确认继续？(y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
    
    # 执行打包流程
    check_environment
    backup_original
    apply_quick_bypass
    modify_app_info
    resign_app
    create_ipa
    
    if verify_ipa; then
        show_completion_info
    else
        log_error "IPA验证失败，请检查文件"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
