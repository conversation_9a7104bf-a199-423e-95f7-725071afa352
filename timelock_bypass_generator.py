#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TimeLock验证码绕过自动化工具
《光遇》外挂验证码弹窗绕过生成器

功能：
- 自动生成绕过Tweak代码
- 编译和安装Tweak
- 生成手动补丁脚本
- 测试验证绕过效果

作者：基于验证码弹窗绕过方案.md
版本：1.0
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json
import time

class TimeLockBypassGenerator:
    def __init__(self, app_path="."):
        """初始化绕过生成器
        
        Args:
            app_path: 应用路径，默认为当前目录
        """
        self.app_path = Path(app_path)
        self.tweak_name = "TimeLockBypass"
        self.version = "1.0.0"
        
        # 确保路径存在
        if not self.app_path.exists():
            raise FileNotFoundError(f"应用路径不存在: {app_path}")
        
        print(f"🚀 TimeLock绕过生成器 v{self.version}")
        print(f"📁 工作目录: {self.app_path.absolute()}")
    
    def check_environment(self):
        """检查编译环境"""
        print("\n🔍 检查编译环境...")
        
        # 检查Theos
        theos_path = os.environ.get('THEOS')
        if not theos_path or not Path(theos_path).exists():
            print("❌ 未找到Theos环境，请先安装Theos")
            print("   安装命令: bash -c \"$(curl -fsSL https://raw.githubusercontent.com/theos/theos/master/bin/install-theos)\"")
            return False
        
        print(f"✅ Theos路径: {theos_path}")
        
        # 检查必要工具
        tools = ['make', 'clang', 'ldid']
        for tool in tools:
            if not shutil.which(tool):
                print(f"❌ 未找到工具: {tool}")
                return False
            print(f"✅ 工具检查: {tool}")
        
        return True
    
    def backup_original_files(self):
        """备份原始文件"""
        print("\n💾 备份原始文件...")
        
        dylib_path = self.app_path / "hhhhsd.dylib"
        backup_path = self.app_path / "hhhhsd.dylib.backup"
        
        if dylib_path.exists():
            if not backup_path.exists():
                shutil.copy2(dylib_path, backup_path)
                print(f"✅ 已备份: {dylib_path} -> {backup_path}")
            else:
                print(f"ℹ️  备份文件已存在: {backup_path}")
        else:
            print(f"⚠️  未找到目标文件: {dylib_path}")
    
    def generate_bypass_tweak(self):
        """生成绕过Tweak代码"""
        print(f"\n📝 生成绕过Tweak代码...")
        
        # 检查是否已存在
        tweak_file = self.app_path / f"{self.tweak_name}.x"
        if tweak_file.exists():
            print(f"ℹ️  Tweak文件已存在: {tweak_file}")
            return True
        
        print(f"❌ 未找到Tweak文件: {tweak_file}")
        print("请确保TimeLockBypass.x文件存在")
        return False
    
    def generate_makefile(self):
        """生成Makefile"""
        print(f"\n📝 检查Makefile...")
        
        makefile_path = self.app_path / "Makefile"
        if makefile_path.exists():
            print(f"ℹ️  Makefile已存在: {makefile_path}")
            return True
        
        print(f"❌ 未找到Makefile: {makefile_path}")
        print("请确保Makefile文件存在")
        return False
    
    def compile_tweak(self):
        """编译Tweak"""
        print(f"\n🔨 编译Tweak...")
        
        try:
            # 切换到工作目录
            os.chdir(self.app_path)
            
            # 清理之前的构建
            print("🧹 清理之前的构建...")
            subprocess.run(["make", "clean"], check=False, capture_output=True)
            
            # 编译
            print("⚙️  开始编译...")
            result = subprocess.run(["make"], check=True, capture_output=True, text=True)
            
            print("✅ 编译成功")
            if result.stdout:
                print(f"输出: {result.stdout}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 编译失败: {e}")
            if e.stdout:
                print(f"标准输出: {e.stdout}")
            if e.stderr:
                print(f"错误输出: {e.stderr}")
            return False
        except Exception as e:
            print(f"❌ 编译过程出错: {e}")
            return False
    
    def install_tweak(self):
        """安装Tweak到设备"""
        print(f"\n📱 安装Tweak到设备...")
        
        try:
            # 检查设备连接
            result = subprocess.run(["idevice_id", "-l"], capture_output=True, text=True)
            if not result.stdout.strip():
                print("❌ 未检测到连接的iOS设备")
                print("请确保设备已连接并信任此电脑")
                return False
            
            device_id = result.stdout.strip().split('\n')[0]
            print(f"📱 检测到设备: {device_id}")
            
            # 安装
            result = subprocess.run(["make", "install"], check=True, capture_output=True, text=True)
            
            print("✅ 安装成功")
            print("📱 请重启Sky-iOS-Gold应用以生效")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            if e.stderr:
                print(f"错误: {e.stderr}")
            return False
        except FileNotFoundError:
            print("❌ 未找到idevice工具，请安装libimobiledevice")
            print("   安装命令: brew install libimobiledevice")
            return False
    
    def create_package(self):
        """创建deb包"""
        print(f"\n📦 创建deb包...")
        
        try:
            result = subprocess.run(["make", "package"], check=True, capture_output=True, text=True)
            
            print("✅ deb包创建成功")
            print("📦 包文件位于 packages/ 目录")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 打包失败: {e}")
            return False
    
    def test_bypass(self):
        """测试绕过效果"""
        print(f"\n🧪 测试绕过效果...")
        
        test_script = self.app_path / "test_bypass.sh"
        if test_script.exists():
            try:
                os.chmod(test_script, 0o755)
                subprocess.run([str(test_script)], check=True)
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ 测试脚本执行失败: {e}")
                return False
        else:
            print("⚠️  未找到测试脚本，手动测试步骤：")
            print("1. 启动Sky-iOS-Gold应用")
            print("2. 触发验证码弹窗的操作")
            print("3. 检查是否还有弹窗出现")
            print("4. 查看日志: idevicesyslog | grep TimeLockBypass")
            return True
    
    def show_status(self):
        """显示当前状态"""
        print(f"\n📊 当前状态:")
        
        files_to_check = [
            ("TimeLockBypass.x", "Hook源文件"),
            ("Makefile", "构建文件"),
            ("test_bypass.sh", "测试脚本"),
            ("manual_timelock_patch.sh", "手动补丁脚本"),
            (".theos/obj/TimeLockBypass.dylib", "编译产物"),
            ("hhhhsd.dylib.backup", "原文件备份")
        ]
        
        for filename, description in files_to_check:
            filepath = self.app_path / filename
            status = "✅" if filepath.exists() else "❌"
            print(f"  {status} {description}: {filename}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 TimeLock验证码绕过自动化工具")
    print("=" * 60)
    
    # 获取应用路径
    app_path = sys.argv[1] if len(sys.argv) > 1 else "."
    
    try:
        generator = TimeLockBypassGenerator(app_path)
        
        # 显示菜单
        while True:
            print(f"\n📋 请选择操作:")
            print("1. 检查环境")
            print("2. 备份原始文件")
            print("3. 编译Tweak")
            print("4. 安装到设备")
            print("5. 创建deb包")
            print("6. 测试绕过效果")
            print("7. 显示状态")
            print("8. 一键完成所有步骤")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-8): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                generator.check_environment()
            elif choice == "2":
                generator.backup_original_files()
            elif choice == "3":
                if generator.generate_bypass_tweak() and generator.generate_makefile():
                    generator.compile_tweak()
            elif choice == "4":
                generator.install_tweak()
            elif choice == "5":
                generator.create_package()
            elif choice == "6":
                generator.test_bypass()
            elif choice == "7":
                generator.show_status()
            elif choice == "8":
                print("\n🚀 开始一键部署...")
                if (generator.check_environment() and
                    generator.backup_original_files() and
                    generator.generate_bypass_tweak() and
                    generator.generate_makefile() and
                    generator.compile_tweak() and
                    generator.install_tweak()):
                    print("\n🎉 部署完成！")
                    generator.test_bypass()
                else:
                    print("\n❌ 部署过程中出现错误")
            else:
                print("❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
