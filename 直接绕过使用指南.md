# 🎯 直接验证码绕过使用指南

## 📖 概述

本工具集可以直接修改验证码逻辑，让外挂插件无需验证码即可运行。验证码检查将自动返回成功，插件功能完全可用。

## 🚀 快速开始（推荐）

### 精确绕过（最新推荐）
```bash
python3 precise_bypass.py
# 选择 "1. 应用精确补丁"
```

基于实际分析结果的最精确方法，成功率最高。

### 一键绕过（备用方案）
```bash
python3 one_click_bypass.py
# 选择 "1. 一键绕过验证码"
```

简单快速的自动化方法。

## 🔧 工具说明

### 1. `precise_bypass.py` - 精确绕过工具（最新推荐）
**功能：** 基于实际分析结果的精确补丁
**使用：**
```bash
python3 precise_bypass.py
```
**特点：**
- ✅ 基于实际发现的verify函数
- ✅ 精确修改TimeLock类
- ✅ 处理base64验证逻辑
- ✅ 最高成功率

### 2. `one_click_bypass.py` - 一键绕过工具
**功能：** 一键完成所有绕过操作
**使用：**
```bash
python3 one_click_bypass.py
```
**特点：**
- ✅ 自动备份原始文件
- ✅ 修改验证函数返回成功
- ✅ 确保插件接收成功信号
- ✅ 自动重新签名

### 3. `verification_patcher.py` - 验证补丁工具
**功能：** 专门修改验证返回值
**使用：**
```bash
python3 verification_patcher.py
```
**特点：**
- 🎯 专注验证函数修改
- 📊 详细的补丁报告
- 🔄 支持恢复操作

### 4. `plugin_interface_patch.py` - 插件接口补丁
**功能：** 修改外挂插件接口
**使用：**
```bash
python3 plugin_interface_patch.py
```
**特点：**
- 🔌 专门处理插件接口
- 📡 确保插件通信正常
- ✅ 让插件直接可用

### 5. `advanced_dylib_analyzer.py` - 高级分析工具
**功能：** 深度分析dylib文件结构
**使用：**
```bash
python3 advanced_dylib_analyzer.py
```
**特点：**
- 🔬 深度分析Mach-O结构
- 🔍 查找符号和字符串
- 💡 生成补丁建议
- 📊 详细的分析报告

## 📱 使用步骤

### 步骤1：选择工具
- **新手用户：** 使用 `one_click_bypass.py`
- **高级用户：** 可以分别使用其他专门工具

### 步骤2：执行绕过
```bash
# 进入应用目录
cd /path/to/Sky-iOS-Gold.app

# 运行一键绕过
python3 one_click_bypass.py

# 选择 "1. 一键绕过验证码"
# 确认操作
```

### 步骤3：重启应用
```bash
# 重启Sky-iOS-Gold应用
# 外挂插件现在可以直接使用
```

## ✨ 绕过效果

修改成功后：
- ✅ **验证码弹窗不再出现**
- ✅ **验证检查自动返回成功**
- ✅ **外挂插件直接可用**
- ✅ **所有功能完全解锁**
- ✅ **无需输入任何验证码**

## 🔄 恢复原始文件

如果需要恢复：
```bash
# 使用工具恢复
python3 one_click_bypass.py
# 选择 "2. 恢复原始文件"

# 或手动恢复
cp hhhhsd.dylib.original hhhhsd.dylib
```

## ⚠️ 注意事项

### 安全提醒
1. **备份重要：** 工具会自动备份，但建议手动再备份一份
2. **测试环境：** 建议先在测试环境使用
3. **应用更新：** 应用更新后可能需要重新绕过

### 兼容性
- ✅ 支持ARM64架构
- ✅ 支持iOS 10.0+
- ✅ 支持越狱和非越狱设备
- ✅ 自动处理代码签名

## 🛠️ 故障排除

### 问题1：工具运行失败
```bash
# 检查Python版本
python3 --version

# 检查文件权限
ls -la *.py

# 重新设置权限
chmod +x *.py
```

### 问题2：修改后应用崩溃
```bash
# 立即恢复原始文件
python3 one_click_bypass.py
# 选择 "2. 恢复原始文件"

# 重启应用测试
```

### 问题3：验证码仍然出现
```bash
# 尝试使用其他工具
python3 verification_patcher.py

# 或使用插件接口补丁
python3 plugin_interface_patch.py
```

## 🔍 技术原理

### 修改原理
1. **定位验证函数：** 查找返回false的验证函数
2. **修改返回值：** 将返回false改为返回true
3. **绕过弹窗：** 阻止验证码弹窗显示
4. **确保通信：** 让插件接收到成功信号

### 修改位置
- TimeLock类的showAlert方法
- 验证码检查函数
- 插件激活检查
- 回调结果设置

## 📞 支持

如果遇到问题：
1. 查看工具输出的错误信息
2. 尝试恢复原始文件
3. 使用不同的绕过工具
4. 检查应用版本兼容性

---

**版本：** 1.0  
**更新时间：** 2025-07-29  
**兼容性：** Sky-iOS-Gold 外挂
