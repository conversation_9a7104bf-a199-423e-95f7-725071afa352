#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外挂插件接口补丁工具
专门修改外挂插件的验证接口，让插件直接接收成功信号

功能：
- 修改插件验证回调函数
- 确保插件接收到验证成功信号
- 绕过所有验证检查
- 让插件功能直接可用

版本：1.0
"""

import os
import sys
import struct
import shutil
from pathlib import Path
import subprocess

class PluginInterfacePatcher:
    def __init__(self, dylib_path="hhhhsd.dylib"):
        self.dylib_path = Path(dylib_path)
        self.backup_path = Path(str(dylib_path) + ".plugin_backup")
        
        print("🔌 外挂插件接口补丁工具")
        print(f"🎯 目标: {self.dylib_path}")
    
    def backup_file(self):
        """备份文件"""
        if not self.backup_path.exists():
            shutil.copy2(self.dylib_path, self.backup_path)
            print(f"✅ 已备份: {self.backup_path}")
        return True
    
    def find_plugin_interfaces(self, data):
        """查找外挂插件接口"""
        print("\n🔍 查找外挂插件接口...")
        
        interfaces = []
        
        # 外挂插件常见的接口关键词
        plugin_keywords = [
            b'plugin',
            b'cheat',
            b'hack',
            b'mod',
            b'inject',
            b'hook',
            b'bypass',
            b'crack'
        ]
        
        # 验证相关的接口
        verification_keywords = [
            b'verify',
            b'check',
            b'validate',
            b'auth',
            b'license',
            b'activate'
        ]
        
        # 回调和状态相关
        callback_keywords = [
            b'callback',
            b'result',
            b'status',
            b'success',
            b'failed',
            b'error',
            b'complete'
        ]
        
        all_keywords = plugin_keywords + verification_keywords + callback_keywords
        
        for keyword in all_keywords:
            offset = 0
            while True:
                pos = data.find(keyword, offset)
                if pos == -1:
                    break
                
                interfaces.append({
                    'keyword': keyword,
                    'offset': pos,
                    'type': self._classify_interface(keyword)
                })
                
                print(f"   🔍 找到接口: {keyword.decode('utf-8', errors='ignore')} 在 0x{pos:x}")
                offset = pos + 1
        
        return interfaces
    
    def _classify_interface(self, keyword):
        """分类接口类型"""
        if keyword in [b'plugin', b'cheat', b'hack', b'mod']:
            return 'plugin'
        elif keyword in [b'verify', b'check', b'validate', b'auth']:
            return 'verification'
        elif keyword in [b'callback', b'result', b'status']:
            return 'callback'
        else:
            return 'other'
    
    def patch_verification_callbacks(self, data, interfaces):
        """修改验证回调，让它们返回成功"""
        print("\n🔧 修改验证回调...")
        
        patches_applied = 0
        
        # 找到验证相关的接口
        verification_interfaces = [i for i in interfaces if i['type'] == 'verification']
        
        for interface in verification_interfaces:
            pos = interface['offset']
            keyword = interface['keyword']
            
            print(f"   🎯 处理验证接口: {keyword.decode('utf-8', errors='ignore')}")
            
            # 在接口附近查找验证逻辑
            search_start = max(0, pos - 2000)
            search_end = min(len(data), pos + 2000)
            
            # 查找验证函数的返回值设置
            for i in range(search_start, search_end - 8):
                # 模式1: mov w0, #0; ret (返回false)
                if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                    data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                    
                    # 改为返回true
                    data[i:i+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                    patches_applied += 1
                    print(f"     ✅ 修改验证返回值在 0x{i:x}")
                
                # 模式2: 设置状态为失败
                elif data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0
                    # 检查是否在验证上下文中
                    context = data[max(0, i-100):i+100]
                    if any(kw in context for kw in [b'verify', b'auth', b'check']):
                        data[i:i+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                        patches_applied += 1
                        print(f"     ✅ 修改状态设置在 0x{i:x}")
        
        return patches_applied
    
    def patch_plugin_activation(self, data, interfaces):
        """修改插件激活逻辑"""
        print("\n🔌 修改插件激活逻辑...")
        
        patches_applied = 0
        
        # 找到插件相关的接口
        plugin_interfaces = [i for i in interfaces if i['type'] == 'plugin']
        
        for interface in plugin_interfaces:
            pos = interface['offset']
            keyword = interface['keyword']
            
            print(f"   🎯 处理插件接口: {keyword.decode('utf-8', errors='ignore')}")
            
            # 在插件接口附近查找激活检查
            search_start = max(0, pos - 1000)
            search_end = min(len(data), pos + 1000)
            
            # 查找可能的激活检查逻辑
            for i in range(search_start, search_end - 4):
                # 查找比较指令后的条件跳转
                if data[i:i+4] == b'\x1f\x00\x00\x6b':  # cmp w0, #0
                    # 查找后续的条件跳转
                    if i + 8 < len(data):
                        next_instr = data[i+4:i+8]
                        # 如果是条件跳转，改为无条件通过
                        if next_instr[3] & 0xf0 == 0x50:  # b.ne, b.eq等条件跳转
                            data[i+4:i+8] = b'\x1f\x20\x03\xd5'  # nop
                            patches_applied += 1
                            print(f"     ✅ 移除激活检查在 0x{i:x}")
        
        return patches_applied
    
    def patch_callback_results(self, data, interfaces):
        """修改回调结果，确保插件接收到成功信号"""
        print("\n📡 修改回调结果...")
        
        patches_applied = 0
        
        # 找到回调相关的接口
        callback_interfaces = [i for i in interfaces if i['type'] == 'callback']
        
        for interface in callback_interfaces:
            pos = interface['offset']
            keyword = interface['keyword']
            
            print(f"   🎯 处理回调接口: {keyword.decode('utf-8', errors='ignore')}")
            
            # 在回调附近查找结果设置
            search_start = max(0, pos - 500)
            search_end = min(len(data), pos + 500)
            
            for i in range(search_start, search_end - 4):
                # 查找设置失败结果的代码
                if data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0 (失败)
                    # 检查是否在回调上下文中
                    context = data[max(0, i-50):i+50]
                    if keyword in context:
                        data[i:i+4] = b'\x20\x00\x80\x52'  # mov w0, #1 (成功)
                        patches_applied += 1
                        print(f"     ✅ 修改回调结果在 0x{i:x}")
        
        return patches_applied
    
    def patch_license_check(self, data):
        """专门修改许可证检查"""
        print("\n🔑 修改许可证检查...")
        
        patches_applied = 0
        
        # 查找许可证相关字符串
        license_strings = [
            b'license',
            b'activate',
            b'expired',
            b'invalid',
            b'trial'
        ]
        
        for license_str in license_strings:
            pos = data.find(license_str)
            if pos != -1:
                print(f"   🔍 找到许可证相关: {license_str.decode()} 在 0x{pos:x}")
                
                # 在附近查找许可证检查逻辑
                search_start = max(0, pos - 1000)
                search_end = min(len(data), pos + 1000)
                
                for i in range(search_start, search_end - 8):
                    # 查找许可证检查失败的返回
                    if (data[i:i+4] == b'\x00\x00\x80\x52' and  # mov w0, #0
                        data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):   # ret
                        
                        # 改为返回成功
                        data[i:i+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                        patches_applied += 1
                        print(f"     ✅ 修改许可证检查在 0x{i:x}")
        
        return patches_applied
    
    def create_always_success_patch(self, data):
        """创建总是成功的补丁"""
        print("\n🎯 创建总是成功补丁...")
        
        patches_applied = 0
        
        # 查找所有可能的验证失败点
        failure_patterns = [
            b'\x00\x00\x80\x52',  # mov w0, #0
            b'\x00\x00\x80\xd2',  # mov x0, #0
        ]
        
        for pattern in failure_patterns:
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                
                # 检查上下文是否与验证/插件相关
                context_start = max(0, pos - 200)
                context_end = min(len(data), pos + 200)
                context = data[context_start:context_end]
                
                # 验证相关的上下文关键词
                context_keywords = [
                    b'verify', b'auth', b'check', b'valid', b'license',
                    b'plugin', b'cheat', b'hack', b'activate', b'trial'
                ]
                
                if any(keyword in context for keyword in context_keywords):
                    # 修改为返回成功
                    if pattern == b'\x00\x00\x80\x52':
                        data[pos:pos+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                    else:
                        data[pos:pos+4] = b'\x20\x00\x80\xd2'  # mov x0, #1
                    
                    patches_applied += 1
                    print(f"   ✅ 修改验证点在 0x{pos:x}")
                
                offset = pos + 1
        
        return patches_applied
    
    def apply_all_patches(self):
        """应用所有补丁"""
        print("\n🚀 开始修改外挂插件接口...")
        
        # 读取文件
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        total_patches = 0
        
        # 1. 查找插件接口
        interfaces = self.find_plugin_interfaces(data)
        
        # 2. 修改验证回调
        total_patches += self.patch_verification_callbacks(data, interfaces)
        
        # 3. 修改插件激活逻辑
        total_patches += self.patch_plugin_activation(data, interfaces)
        
        # 4. 修改回调结果
        total_patches += self.patch_callback_results(data, interfaces)
        
        # 5. 修改许可证检查
        total_patches += self.patch_license_check(data)
        
        # 6. 创建总是成功的补丁
        total_patches += self.create_always_success_patch(data)
        
        if total_patches > 0:
            # 保存修改后的文件
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            
            print(f"\n✅ 总共应用了 {total_patches} 个插件接口补丁")
            
            # 重新签名
            self.resign_file()
            
            return True
        else:
            print("\n❌ 未找到可修改的插件接口")
            return False
    
    def resign_file(self):
        """重新签名"""
        try:
            subprocess.run(['codesign', '--remove-signature', str(self.dylib_path)], 
                         check=False, capture_output=True)
            subprocess.run(['codesign', '-f', '-s', '-', str(self.dylib_path)], 
                         capture_output=True)
            print("✅ 重新签名完成")
        except:
            print("⚠️  签名可能有问题，但文件仍可使用")
    
    def restore_backup(self):
        """恢复备份"""
        if self.backup_path.exists():
            shutil.copy2(self.backup_path, self.dylib_path)
            print("✅ 已恢复原始文件")
            return True
        return False

def main():
    print("=" * 60)
    print("🔌 外挂插件接口补丁工具")
    print("让外挂插件直接运行，无需验证")
    print("=" * 60)
    
    dylib_path = sys.argv[1] if len(sys.argv) > 1 else "hhhhsd.dylib"
    
    try:
        patcher = PluginInterfacePatcher(dylib_path)
        
        print("\n📋 选择操作:")
        print("1. 修改插件接口（让插件直接运行）")
        print("2. 恢复原始文件")
        print("0. 退出")
        
        choice = input("\n请选择 (0-2): ").strip()
        
        if choice == "1":
            print("\n⚠️  即将修改外挂插件接口")
            print("修改后外挂插件将直接运行，无需验证码")
            confirm = input("确认继续？(y/N): ").strip().lower()
            
            if confirm == 'y':
                if patcher.backup_file() and patcher.apply_all_patches():
                    print("\n🎉 插件接口修改成功！")
                    print("\n📱 使用说明:")
                    print("1. 重启Sky-iOS-Gold应用")
                    print("2. 外挂插件现在可以直接使用")
                    print("3. 所有验证检查都会返回成功")
                    print("4. 享受无限制的插件功能！")
                else:
                    print("\n❌ 修改失败")
            else:
                print("❌ 用户取消操作")
                
        elif choice == "2":
            patcher.restore_backup()
            
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
    
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
