#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
永久验证码绕过和IPA重新打包工具
永久性地修改应用，跳过所有验证码弹窗，直接启用外挂工具，并重新打包成IPA

功能：
- 永久修改验证码逻辑
- 直接启用所有外挂功能
- 重新签名和打包IPA
- 生成可直接安装的应用

版本：1.0
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
import plistlib
import time

class PermanentBypassAndRepack:
    def __init__(self, app_path="."):
        self.app_path = Path(app_path).resolve()
        self.app_name = "Sky-iOS-Gold"
        self.dylib_path = self.app_path / "hhhhsd.dylib"
        self.info_plist_path = self.app_path / "Info.plist"
        
        # 输出目录
        self.output_dir = self.app_path.parent / "Modified_IPA"
        self.ipa_path = self.output_dir / f"{self.app_name}_Bypassed.ipa"
        
        print("🎯 永久验证码绕过和IPA重新打包工具")
        print("=" * 60)
        print(f"📁 应用路径: {self.app_path}")
        print(f"🎯 目标文件: {self.dylib_path}")
        print(f"📦 输出IPA: {self.ipa_path}")
    
    def check_environment(self):
        """检查环境和必要文件"""
        print("\n🔍 检查环境...")
        
        # 检查必要文件
        required_files = [
            self.dylib_path,
            self.info_plist_path,
            self.app_path / self.app_name
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                print(f"❌ 缺少必要文件: {file_path}")
                return False
            print(f"✅ 找到文件: {file_path.name}")
        
        # 检查必要工具
        tools = ['codesign', 'zip', 'plutil']
        for tool in tools:
            if not shutil.which(tool):
                print(f"❌ 缺少工具: {tool}")
                return False
            print(f"✅ 工具检查: {tool}")
        
        return True
    
    def backup_original_app(self):
        """备份原始应用"""
        print("\n💾 备份原始应用...")
        
        backup_dir = self.app_path.parent / f"{self.app_name}_Original_Backup"
        
        if not backup_dir.exists():
            shutil.copytree(self.app_path, backup_dir)
            print(f"✅ 已备份到: {backup_dir}")
        else:
            print(f"ℹ️  备份已存在: {backup_dir}")
        
        return True
    
    def apply_permanent_bypass(self):
        """应用永久验证码绕过"""
        print("\n🔧 应用永久验证码绕过...")
        
        # 读取dylib文件
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        original_size = len(data)
        patches_applied = 0
        
        print("   🎯 修改验证函数...")
        
        # 1. 修改verify函数 - 基于之前的分析结果
        verify_pos = data.find(b'verify')
        if verify_pos != -1:
            print(f"   ✅ 找到verify函数在: 0x{verify_pos:x}")
            
            # 在verify附近查找验证逻辑
            search_start = max(0, verify_pos - 5000)
            search_end = min(len(data), verify_pos + 5000)
            
            for i in range(search_start, search_end - 8, 4):
                # 查找 mov w0, #0; ret 模式（返回失败）
                if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                    data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                    
                    # 改为 mov w0, #1; ret（永久返回成功）
                    data[i:i+4] = b'\x20\x00\x80\x52'
                    patches_applied += 1
                    print(f"     ✅ 永久修改验证返回值在 0x{i:x}")
        
        # 2. 修改TimeLock类 - 永久禁用弹窗
        timelock_pos = data.find(b'TimeLock')
        if timelock_pos != -1:
            print(f"   ✅ 找到TimeLock类在: 0x{timelock_pos:x}")
            
            # 在TimeLock附近查找showAlert函数
            search_start = max(0, timelock_pos - 10000)
            search_end = min(len(data), timelock_pos + 10000)
            
            for i in range(search_start, search_end - 4, 4):
                # 查找函数调用指令（bl）
                if data[i+3] == 0x94:  # bl指令
                    # 将函数调用改为NOP，永久禁用弹窗
                    data[i:i+4] = b'\x1f\x20\x03\xd5'  # nop
                    patches_applied += 1
                    print(f"     ✅ 永久禁用弹窗调用在 0x{i:x}")
        
        # 3. 修改所有验证相关的字符串
        print("   🔧 修改验证字符串...")
        
        verification_strings = [
            (b'\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81', b'SUCCESS_OK'),  # "验证码"
            (b'\xe9\xaa\x8c\xe8\xaf\x81', b'PASS'),                    # "验证"
            (b'verification', b'BYPASSED___'),
            (b'activate', b'ENABLED_'),
            (b'license', b'CRACKED'),
        ]
        
        for old_str, new_str in verification_strings:
            if len(new_str) <= len(old_str):
                # 确保新字符串不超过原长度
                new_str = new_str.ljust(len(old_str), b'\x00')
                count = data.count(old_str)
                if count > 0:
                    data = data.replace(old_str, new_str)
                    patches_applied += count
                    print(f"     ✅ 永久替换字符串: {old_str} -> {new_str} ({count}次)")
        
        # 4. 永久启用外挂功能
        print("   🔌 永久启用外挂功能...")
        
        # 查找可能的激活开关
        activation_patterns = [
            b'enabled',
            b'active',
            b'unlocked',
            b'premium'
        ]
        
        for pattern in activation_patterns:
            pos = data.find(pattern)
            if pos != -1:
                # 在附近查找布尔值设置
                search_start = max(0, pos - 200)
                search_end = min(len(data), pos + 200)
                
                for i in range(search_start, search_end - 4):
                    if data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0 (false)
                        data[i:i+4] = b'\x20\x00\x80\x52'   # mov w0, #1 (true)
                        patches_applied += 1
                        print(f"     ✅ 永久启用功能在 0x{i:x}")
        
        # 保存修改后的文件
        if patches_applied > 0:
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            
            print(f"\n✅ 总共应用了 {patches_applied} 个永久补丁")
            print(f"📊 文件大小: {original_size} -> {len(data)} 字节")
            return True
        else:
            print("\n❌ 未找到可修改的验证逻辑")
            return False
    
    def modify_app_info(self):
        """修改应用信息"""
        print("\n📝 修改应用信息...")
        
        try:
            # 读取Info.plist
            with open(self.info_plist_path, 'rb') as f:
                plist_data = plistlib.load(f)
            
            # 修改应用信息，标记为已修改版本
            original_name = plist_data.get('CFBundleDisplayName', self.app_name)
            plist_data['CFBundleDisplayName'] = f"{original_name} Bypassed"
            
            # 修改版本号
            original_version = plist_data.get('CFBundleShortVersionString', '1.0')
            plist_data['CFBundleShortVersionString'] = f"{original_version}.bypassed"
            
            # 添加自定义标识
            plist_data['BypassedVersion'] = "1.0"
            plist_data['BypassedDate'] = time.strftime("%Y-%m-%d")
            
            # 保存修改后的plist
            with open(self.info_plist_path, 'wb') as f:
                plistlib.dump(plist_data, f)
            
            print(f"✅ 应用名称: {original_name} -> {plist_data['CFBundleDisplayName']}")
            print(f"✅ 版本号: {original_version} -> {plist_data['CFBundleShortVersionString']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 修改应用信息失败: {e}")
            return False
    
    def resign_app(self):
        """重新签名应用"""
        print("\n✍️  重新签名应用...")
        
        try:
            # 移除现有签名
            subprocess.run(['codesign', '--remove-signature', str(self.dylib_path)], 
                         check=False, capture_output=True)
            
            subprocess.run(['codesign', '--remove-signature', str(self.app_path / self.app_name)], 
                         check=False, capture_output=True)
            
            # 重新签名dylib
            result = subprocess.run(['codesign', '-f', '-s', '-', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ dylib重新签名成功")
            else:
                print(f"⚠️  dylib签名警告: {result.stderr}")
            
            # 重新签名主程序
            result = subprocess.run(['codesign', '-f', '-s', '-', str(self.app_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 应用重新签名成功")
            else:
                print(f"⚠️  应用签名警告: {result.stderr}")
            
            return True
            
        except Exception as e:
            print(f"❌ 重新签名失败: {e}")
            return False
    
    def create_ipa_package(self):
        """创建IPA包"""
        print("\n📦 创建IPA包...")

        try:
            # 创建输出目录
            self.output_dir.mkdir(exist_ok=True)

            # 创建Payload目录结构
            payload_dir = self.output_dir / "Payload"
            if payload_dir.exists():
                shutil.rmtree(payload_dir)
            payload_dir.mkdir()

            # 复制修改后的应用到Payload目录
            app_in_payload = payload_dir / f"{self.app_name}.app"
            shutil.copytree(self.app_path, app_in_payload)

            print(f"✅ 应用已复制到: {app_in_payload}")

            # 添加额外的绕过标记文件
            bypass_info = app_in_payload / "bypass_info.txt"
            with open(bypass_info, 'w', encoding='utf-8') as f:
                f.write("验证码永久绕过版本\n")
                f.write(f"修改时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("外挂功能: 永久启用\n")
                f.write("验证码: 永久绕过\n")

            # 创建ZIP文件（IPA实际上是ZIP格式）
            if self.ipa_path.exists():
                self.ipa_path.unlink()

            print("📦 正在压缩文件...")
            with zipfile.ZipFile(self.ipa_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                # 添加Payload目录下的所有文件
                total_files = 0
                for root, dirs, files in os.walk(payload_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_name = file_path.relative_to(self.output_dir)
                        zipf.write(file_path, arc_name)
                        total_files += 1

                        if total_files % 100 == 0:
                            print(f"   已处理 {total_files} 个文件...")

            # 清理临时目录
            shutil.rmtree(payload_dir)

            ipa_size = self.ipa_path.stat().st_size
            print(f"✅ IPA包创建成功: {self.ipa_path}")
            print(f"📊 IPA大小: {ipa_size:,} 字节 ({ipa_size/1024/1024:.1f} MB)")
            print(f"📁 包含文件: {total_files} 个")

            return True

        except Exception as e:
            print(f"❌ IPA包创建失败: {e}")
            return False
    
    def verify_ipa_package(self):
        """验证IPA包"""
        print("\n🧪 验证IPA包...")
        
        try:
            # 检查IPA文件
            if not self.ipa_path.exists():
                print("❌ IPA文件不存在")
                return False
            
            # 验证ZIP结构
            with zipfile.ZipFile(self.ipa_path, 'r') as zipf:
                file_list = zipf.namelist()
                
                # 检查必要的文件
                required_files = [
                    f'Payload/{self.app_name}.app/{self.app_name}',
                    f'Payload/{self.app_name}.app/Info.plist',
                    f'Payload/{self.app_name}.app/hhhhsd.dylib'
                ]
                
                for req_file in required_files:
                    if req_file in file_list:
                        print(f"✅ 包含文件: {req_file}")
                    else:
                        print(f"❌ 缺少文件: {req_file}")
                        return False
            
            print("✅ IPA包结构验证通过")
            return True
            
        except Exception as e:
            print(f"❌ IPA验证失败: {e}")
            return False
    
    def show_installation_guide(self):
        """显示安装指南"""
        print("\n" + "=" * 60)
        print("🎉 永久绕过IPA创建成功！")
        print("=" * 60)
        print()
        print("📱 安装方法:")
        print("1. 使用AltStore/Sideloadly安装:")
        print(f"   - 选择文件: {self.ipa_path}")
        print("   - 安装到设备")
        print()
        print("2. 使用Cydia Impactor:")
        print(f"   - 拖拽 {self.ipa_path.name} 到Impactor")
        print("   - 输入Apple ID安装")
        print()
        print("3. 使用3uTools等工具:")
        print(f"   - 导入 {self.ipa_path.name}")
        print("   - 安装到设备")
        print()
        print("✨ 修改效果:")
        print("✅ 验证码弹窗永久消失")
        print("✅ 外挂工具直接启用")
        print("✅ 所有功能完全解锁")
        print("✅ 无需任何验证操作")
        print()
        print("⚠️  注意事项:")
        print("- 安装前请卸载原版应用")
        print("- 首次启动可能需要信任开发者")
        print("- 建议备份游戏数据")
        print()
    
    def run_permanent_bypass(self):
        """执行完整的永久绕过流程"""
        print("🚀 开始永久验证码绕过和IPA重新打包...")
        
        if (self.check_environment() and
            self.backup_original_app() and
            self.apply_permanent_bypass() and
            self.modify_app_info() and
            self.resign_app() and
            self.create_ipa_package() and
            self.verify_ipa_package()):
            
            self.show_installation_guide()
            return True
        else:
            print("\n❌ 永久绕过流程失败")
            return False

def main():
    print("=" * 60)
    print("🎯 永久验证码绕过和IPA重新打包工具")
    print("创建永久绕过验证码的IPA文件")
    print("=" * 60)
    
    app_path = sys.argv[1] if len(sys.argv) > 1 else "."
    
    try:
        repack_tool = PermanentBypassAndRepack(app_path)
        
        print("\n📋 选择操作:")
        print("1. 🚀 执行永久绕过并重新打包IPA")
        print("2. 📊 仅检查环境和文件")
        print("0. 退出")
        
        choice = input("\n请选择 (0-2): ").strip()
        
        if choice == "1":
            print("\n⚠️  即将执行永久修改并重新打包")
            print("这将创建一个永久绕过验证码的IPA文件")
            print("原始应用将被自动备份")
            print()
            confirm = input("确认开始永久绕过和重新打包？(y/N): ").strip().lower()
            
            if confirm == 'y':
                if repack_tool.run_permanent_bypass():
                    print(f"\n🎉 成功！IPA文件已创建: {repack_tool.ipa_path}")
                else:
                    print("\n❌ 操作失败")
            else:
                print("❌ 用户取消操作")
                
        elif choice == "2":
            repack_tool.check_environment()
            
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
