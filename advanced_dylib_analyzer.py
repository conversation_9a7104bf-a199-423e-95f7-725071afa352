#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级dylib分析工具
深度分析hhhhsd.dylib，找到验证码相关的精确位置

功能：
- 深度分析Mach-O结构
- 精确定位验证函数
- 分析函数调用关系
- 生成精确的补丁方案

版本：1.0
"""

import os
import sys
import struct
import subprocess
from pathlib import Path

class AdvancedDylibAnalyzer:
    def __init__(self, dylib_path="hhhhsd.dylib"):
        self.dylib_path = Path(dylib_path)
        print(f"🔬 高级dylib分析工具")
        print(f"🎯 分析目标: {self.dylib_path}")
    
    def analyze_mach_o_structure(self):
        """分析Mach-O文件结构"""
        print("\n🔍 分析Mach-O文件结构...")
        
        try:
            # 使用otool分析文件结构
            result = subprocess.run(['otool', '-h', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("📋 Mach-O头信息:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"   {line}")
            
            # 分析段信息
            result = subprocess.run(['otool', '-l', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("\n📋 段信息:")
                lines = result.stdout.split('\n')
                for i, line in enumerate(lines):
                    if 'segname' in line or 'sectname' in line:
                        print(f"   {line.strip()}")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    def find_symbols(self):
        """查找符号表"""
        print("\n🔍 查找符号表...")
        
        try:
            # 使用nm查找符号
            result = subprocess.run(['nm', '-D', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                symbols = []
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if any(keyword in line.lower() for keyword in 
                          ['timelock', 'alert', 'verify', 'auth', 'check']):
                        symbols.append(line)
                
                if symbols:
                    print(f"📋 找到 {len(symbols)} 个相关符号:")
                    for symbol in symbols:
                        print(f"   {symbol}")
                else:
                    print("⚠️  未找到相关符号")
            
            # 使用objdump查找更多信息
            result = subprocess.run(['objdump', '-t', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("\n📋 objdump符号信息:")
                for line in result.stdout.split('\n'):
                    if any(keyword in line.lower() for keyword in 
                          ['timelock', 'alert', 'verify']):
                        print(f"   {line.strip()}")
        
        except Exception as e:
            print(f"❌ 符号查找失败: {e}")
    
    def analyze_strings(self):
        """分析字符串"""
        print("\n🔍 分析字符串...")
        
        try:
            result = subprocess.run(['strings', str(self.dylib_path)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                verification_strings = []
                plugin_strings = []
                
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if len(line) < 3:
                        continue
                    
                    # 验证相关字符串
                    if any(keyword in line.lower() for keyword in 
                          ['验证', 'verify', 'code', 'auth', 'license', 'activate']):
                        verification_strings.append(line)
                    
                    # 插件相关字符串
                    elif any(keyword in line.lower() for keyword in 
                            ['plugin', 'cheat', 'hack', 'mod', 'inject']):
                        plugin_strings.append(line)
                
                print(f"📋 验证相关字符串 ({len(verification_strings)}个):")
                for s in verification_strings[:10]:
                    print(f"   {s}")
                
                print(f"\n📋 插件相关字符串 ({len(plugin_strings)}个):")
                for s in plugin_strings[:10]:
                    print(f"   {s}")
        
        except Exception as e:
            print(f"❌ 字符串分析失败: {e}")
    
    def find_function_addresses(self):
        """查找函数地址"""
        print("\n🎯 查找函数地址...")
        
        with open(self.dylib_path, 'rb') as f:
            data = f.read()
        
        # 查找TimeLock类相关的地址
        timelock_pos = data.find(b'TimeLock')
        if timelock_pos != -1:
            print(f"🔍 TimeLock类字符串在: 0x{timelock_pos:x}")
            
            # 查找showAlert相关
            alert_patterns = [b'showAlert', b'showAlert2']
            for pattern in alert_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"🔍 {pattern.decode()}字符串在: 0x{pos:x}")
        
        # 查找验证相关的函数模式
        print("\n🔍 查找验证函数模式...")
        
        # ARM64函数开始模式
        function_patterns = [
            b'\xff\x03\x01\xd1',  # sub sp, sp, #0x40
            b'\xfd\x7b\xbf\xa9',  # stp x29, x30, [sp, #-0x10]!
            b'\xfd\x03\x00\x91',  # mov x29, sp
        ]
        
        for i, pattern in enumerate(function_patterns):
            offset = 0
            count = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                
                # 检查附近是否有验证相关的字符串引用
                context_start = max(0, pos - 1000)
                context_end = min(len(data), pos + 1000)
                context = data[context_start:context_end]
                
                if any(keyword in context for keyword in 
                      [b'verify', b'auth', b'TimeLock', b'alert']):
                    print(f"   🎯 可能的验证函数在: 0x{pos:x} (模式{i+1})")
                    count += 1
                    
                    if count >= 5:  # 限制输出数量
                        break
                
                offset = pos + 1
    
    def analyze_verification_flow(self):
        """分析验证流程"""
        print("\n🔄 分析验证流程...")
        
        with open(self.dylib_path, 'rb') as f:
            data = f.read()
        
        # 查找验证码相关的字符串
        verification_strings = [
            b'\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81',  # "验证码"
            b'\xe9\xaa\x8c\xe8\xaf\x81',              # "验证"
            b'verification',
            b'activate',
            b'license'
        ]
        
        for verify_str in verification_strings:
            pos = data.find(verify_str)
            if pos != -1:
                print(f"🔍 找到验证字符串: {verify_str} 在 0x{pos:x}")
                
                # 查找引用这个字符串的代码
                # 在ARM64中，字符串通常通过adrp + add指令加载
                self._find_string_references(data, pos, verify_str)
    
    def _find_string_references(self, data, string_pos, string_value):
        """查找字符串引用"""
        print(f"   🔍 查找 {string_value} 的引用...")
        
        # 简化的字符串引用查找
        # 在实际的ARM64代码中，需要更复杂的分析
        search_start = max(0, string_pos - 10000)
        search_end = min(len(data), string_pos + 10000)
        
        # 查找可能的函数调用模式
        for i in range(search_start, search_end - 4, 4):  # 按4字节对齐
            # 查找bl指令 (0x94xxxxxx)
            if data[i+3] == 0x94:
                print(f"     🎯 可能的函数调用在: 0x{i:x}")
    
    def generate_patch_recommendations(self):
        """生成补丁建议"""
        print("\n💡 生成补丁建议...")
        
        with open(self.dylib_path, 'rb') as f:
            data = f.read()
        
        recommendations = []
        
        # 查找返回false的模式
        false_return_patterns = [
            (b'\x00\x00\x80\x52\xc0\x03\x5f\xd6', "mov w0, #0; ret"),
            (b'\x00\x00\x80\xd2\xc0\x03\x5f\xd6', "mov x0, #0; ret"),
        ]
        
        for pattern, desc in false_return_patterns:
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                
                # 检查上下文
                context_start = max(0, pos - 500)
                context_end = min(len(data), pos + 500)
                context = data[context_start:context_end]
                
                # 计算相关性分数
                score = 0
                keywords = [b'verify', b'auth', b'TimeLock', b'alert', b'check']
                for keyword in keywords:
                    if keyword in context:
                        score += 1
                
                if score > 0:
                    recommendations.append({
                        'offset': pos,
                        'pattern': pattern,
                        'description': desc,
                        'score': score,
                        'patch': pattern.replace(b'\x00\x00\x80\x52', b'\x20\x00\x80\x52')
                    })
                
                offset = pos + 1
        
        # 按相关性排序
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📋 找到 {len(recommendations)} 个补丁建议:")
        for i, rec in enumerate(recommendations[:10]):  # 只显示前10个
            print(f"   {i+1}. 0x{rec['offset']:x}: {rec['description']} (相关性: {rec['score']})")
            print(f"      原始: {rec['pattern'].hex()}")
            print(f"      补丁: {rec['patch'].hex()}")
        
        return recommendations
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始完整分析...")
        
        if not self.dylib_path.exists():
            print(f"❌ 文件不存在: {self.dylib_path}")
            return
        
        # 执行所有分析
        self.analyze_mach_o_structure()
        self.find_symbols()
        self.analyze_strings()
        self.find_function_addresses()
        self.analyze_verification_flow()
        recommendations = self.generate_patch_recommendations()
        
        print(f"\n📊 分析完成")
        print(f"文件大小: {self.dylib_path.stat().st_size:,} 字节")
        print(f"补丁建议: {len(recommendations)} 个")

def main():
    print("=" * 60)
    print("🔬 高级dylib分析工具")
    print("深度分析验证码相关函数")
    print("=" * 60)
    
    dylib_path = sys.argv[1] if len(sys.argv) > 1 else "hhhhsd.dylib"
    
    try:
        analyzer = AdvancedDylibAnalyzer(dylib_path)
        
        print("\n📋 选择分析类型:")
        print("1. 🔬 完整深度分析")
        print("2. 🔍 仅查找符号")
        print("3. 📝 仅分析字符串")
        print("4. 💡 生成补丁建议")
        print("0. 退出")
        
        choice = input("\n请选择 (0-4): ").strip()
        
        if choice == "1":
            analyzer.run_full_analysis()
        elif choice == "2":
            analyzer.find_symbols()
        elif choice == "3":
            analyzer.analyze_strings()
        elif choice == "4":
            analyzer.generate_patch_recommendations()
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
    
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
