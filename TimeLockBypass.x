/*
 * TimeLockBypass.x
 * 《光遇》验证码弹窗绕过 Substrate Hook
 * 
 * 功能：拦截并阻止验证码相关弹窗的显示
 * 方案：多层次Hook确保绕过效果
 * 
 * 作者：基于验证码弹窗绕过方案.md
 * 版本：1.0
 */

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>

// 日志宏定义
#define TLBLog(fmt, ...) NSLog(@"[TimeLockBypass] " fmt, ##__VA_ARGS__)

// 验证码相关关键词
static NSArray *verificationKeywords = nil;

%ctor {
    TLBLog("验证码绕过模块已加载 - 版本 1.0");
    
    // 初始化关键词列表
    verificationKeywords = @[
        @"验证", @"code", @"verification", @"auth", @"密码",
        @"激活", @"activate", @"license", @"key", @"unlock",
        @"注册", @"register", @"login", @"登录"
    ];
    
    TLBLog("已加载 %lu 个验证关键词", (unsigned long)[verificationKeywords count]);
}

// ============================================================================
// 方案1: Hook TimeLock类 - 最直接的方案
// ============================================================================

%hook TimeLock

// Hook showAlert方法，直接返回不显示弹窗
- (void)showAlertWithMessage:(NSString *)message {
    TLBLog("🚫 拦截TimeLock验证码弹窗: %@", message);
    
    // 记录拦截信息到系统日志
    NSLog(@"[BYPASS] TimeLock.showAlert blocked: %@", message);
    
    // 不调用 %orig，直接返回，阻止弹窗显示
    return;
}

// Hook showAlert2方法
- (void)showAlert2WithMessage:(NSString *)message {
    TLBLog("🚫 拦截TimeLock验证码弹窗2: %@", message);
    
    // 记录拦截信息到系统日志
    NSLog(@"[BYPASS] TimeLock.showAlert2 blocked: %@", message);
    
    return;
}

// Hook定时器相关方法，防止定时弹窗
- (void)startTimer {
    TLBLog("🚫 拦截TimeLock定时器启动");
    // 不启动定时器
    return;
}

- (void)stopTimer {
    TLBLog("ℹ️ TimeLock定时器停止调用");
    %orig; // 允许停止定时器
}

%end

// ============================================================================
// 方案2: Hook UIAlertController - 通用弹窗拦截方案
// ============================================================================

%hook UIAlertController

+ (instancetype)alertControllerWithTitle:(NSString *)title 
                                 message:(NSString *)message 
                          preferredStyle:(UIAlertControllerStyle)preferredStyle {
    
    // 安全检查
    if (!title) title = @"";
    if (!message) message = @"";
    
    // 检查是否为验证码相关弹窗
    BOOL isVerificationAlert = NO;
    
    for (NSString *keyword in verificationKeywords) {
        if ([title.lowercaseString containsString:keyword.lowercaseString] || 
            [message.lowercaseString containsString:keyword.lowercaseString]) {
            isVerificationAlert = YES;
            break;
        }
    }
    
    if (isVerificationAlert) {
        TLBLog("🚫 拦截UIAlertController验证码弹窗");
        TLBLog("   标题: %@", title);
        TLBLog("   消息: %@", message);
        
        // 记录到系统日志
        NSLog(@"[BYPASS] UIAlertController blocked - Title: %@, Message: %@", title, message);
        
        // 返回nil，阻止弹窗创建
        return nil;
    }
    
    // 其他弹窗正常显示
    return %orig(title, message, preferredStyle);
}

%end

// ============================================================================
// 方案3: Hook UIViewController present方法 - 最后保险方案
// ============================================================================

%hook UIViewController

- (void)presentViewController:(UIViewController *)viewControllerToPresent 
                     animated:(BOOL)flag 
                   completion:(void (^)(void))completion {
    
    // 检查是否为UIAlertController
    if ([viewControllerToPresent isKindOfClass:[UIAlertController class]]) {
        UIAlertController *alert = (UIAlertController *)viewControllerToPresent;
        
        // 安全获取标题和消息
        NSString *title = alert.title ?: @"";
        NSString *message = alert.message ?: @"";
        
        // 检查弹窗内容
        BOOL isVerificationAlert = NO;
        
        for (NSString *keyword in verificationKeywords) {
            if ([title.lowercaseString containsString:keyword.lowercaseString] || 
                [message.lowercaseString containsString:keyword.lowercaseString]) {
                isVerificationAlert = YES;
                break;
            }
        }
        
        if (isVerificationAlert) {
            TLBLog("🚫 阻止UIViewController present验证码弹窗");
            TLBLog("   标题: %@", title);
            TLBLog("   消息: %@", message);
            
            // 记录到系统日志
            NSLog(@"[BYPASS] UIViewController.present blocked - Title: %@, Message: %@", title, message);
            
            // 直接调用completion，不显示弹窗
            if (completion) {
                completion();
            }
            return;
        }
    }
    
    // 其他视图控制器正常显示
    %orig(viewControllerToPresent, flag, completion);
}

%end

// ============================================================================
// 方案4: Hook NSTimer - 防止定时验证
// ============================================================================

%hook NSTimer

+ (NSTimer *)scheduledTimerWithTimeInterval:(NSTimeInterval)ti 
                                     target:(id)aTarget 
                                   selector:(SEL)aSelector 
                                   userInfo:(id)userInfo 
                                    repeats:(BOOL)yesOrNo {
    
    // 检查是否为TimeLock相关的定时器
    if (aTarget && ([aTarget isKindOfClass:NSClassFromString(@"TimeLock")] ||
                   [aTarget isKindOfClass:NSClassFromString(@"MyTimer")])) {
        
        TLBLog("🚫 拦截TimeLock相关定时器创建");
        TLBLog("   目标类: %@", NSStringFromClass([aTarget class]));
        TLBLog("   选择器: %@", NSStringFromSelector(aSelector));
        TLBLog("   间隔: %.2f秒", ti);
        
        // 记录到系统日志
        NSLog(@"[BYPASS] NSTimer blocked for TimeLock - Target: %@, Selector: %@", 
              NSStringFromClass([aTarget class]), NSStringFromSelector(aSelector));
        
        // 返回一个无效的定时器
        return nil;
    }
    
    return %orig(ti, aTarget, aSelector, userInfo, yesOrNo);
}

%end

// ============================================================================
// 额外保护：Hook常见的弹窗显示方法
// ============================================================================

%hook UIAlertView

- (void)show {
    // 检查是否为验证码相关弹窗（iOS 9之前的方式）
    NSString *title = self.title ?: @"";
    NSString *message = self.message ?: @"";
    
    for (NSString *keyword in verificationKeywords) {
        if ([title.lowercaseString containsString:keyword.lowercaseString] || 
            [message.lowercaseString containsString:keyword.lowercaseString]) {
            
            TLBLog("🚫 拦截UIAlertView验证码弹窗");
            TLBLog("   标题: %@", title);
            TLBLog("   消息: %@", message);
            
            NSLog(@"[BYPASS] UIAlertView blocked - Title: %@, Message: %@", title, message);
            return;
        }
    }
    
    %orig;
}

%end

// ============================================================================
// 调试和监控功能
// ============================================================================

// 监控所有弹窗创建，用于调试
%hook UIWindow

- (void)makeKeyAndVisible {
    TLBLog("🔍 窗口显示: %@", NSStringFromClass([self class]));
    %orig;
}

%end

// 记录应用生命周期，便于调试
%hook UIApplication

- (void)applicationDidBecomeActive:(UIApplication *)application {
    TLBLog("📱 应用变为活跃状态");
    %orig;
}

- (void)applicationWillResignActive:(UIApplication *)application {
    TLBLog("📱 应用即将失去活跃状态");
    %orig;
}

%end
