#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确验证码绕过工具
基于实际分析结果，精确修改验证逻辑

发现的关键信息：
- 找到 "verify" 字符串
- 找到 "base64EncodedStringWithOptions:" 
- TimeLock类存在

策略：
1. 找到verify函数的实现
2. 修改验证逻辑直接返回成功
3. 确保外挂插件接收到成功信号

版本：1.0
"""

import os
import sys
import shutil
from pathlib import Path
import subprocess

class PreciseBypass:
    def __init__(self, dylib_path="hhhhsd.dylib"):
        self.dylib_path = Path(dylib_path)
        self.backup_path = Path(str(dylib_path) + ".precise_backup")
        
        print("🎯 精确验证码绕过工具")
        print(f"🎯 目标: {self.dylib_path}")
    
    def backup_file(self):
        """备份文件"""
        if not self.backup_path.exists():
            shutil.copy2(self.dylib_path, self.backup_path)
            print(f"✅ 已备份: {self.backup_path}")
        return True
    
    def find_verify_function(self, data):
        """查找verify函数"""
        print("\n🔍 查找verify函数...")
        
        verify_pos = data.find(b'verify')
        if verify_pos == -1:
            print("❌ 未找到verify字符串")
            return []
        
        print(f"✅ 找到verify字符串在: 0x{verify_pos:x}")
        
        # 查找引用verify字符串的代码位置
        verify_refs = []
        
        # 在verify字符串附近查找可能的函数
        search_start = max(0, verify_pos - 5000)
        search_end = min(len(data), verify_pos + 5000)
        
        print(f"🔍 在范围 0x{search_start:x} - 0x{search_end:x} 查找verify函数...")
        
        # 查找ARM64函数模式
        for i in range(search_start, search_end - 8, 4):
            # 查找函数开始模式: stp x29, x30, [sp, #-0x10]!
            if data[i:i+4] == b'\xfd\x7b\xbf\xa9':
                print(f"   🎯 可能的函数开始在: 0x{i:x}")
                
                # 查找这个函数的结束（ret指令）
                for j in range(i, min(i + 1000, len(data) - 4), 4):
                    if data[j:j+4] == b'\xc0\x03\x5f\xd6':  # ret
                        func_size = j - i + 4
                        print(f"     📏 函数大小: {func_size} 字节")
                        
                        # 检查函数内是否引用了verify
                        func_data = data[i:j+4]
                        if b'verify' in func_data or verify_pos in range(i, j+4):
                            verify_refs.append({
                                'start': i,
                                'end': j + 4,
                                'size': func_size
                            })
                            print(f"     ✅ 找到verify相关函数: 0x{i:x} - 0x{j+4:x}")
                        break
        
        return verify_refs
    
    def patch_verify_functions(self, data, verify_refs):
        """修改verify函数"""
        print(f"\n🔧 修改 {len(verify_refs)} 个verify函数...")
        
        patches_applied = 0
        
        for ref in verify_refs:
            start = ref['start']
            end = ref['end']
            size = ref['size']
            
            print(f"   🎯 处理函数 0x{start:x} - 0x{end:x} ({size}字节)")
            
            # 在函数内查找返回值设置
            for i in range(start, end - 8, 4):
                # 查找 mov w0, #0; ret 模式
                if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                    data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                    
                    # 改为 mov w0, #1; ret (返回成功)
                    data[i:i+4] = b'\x20\x00\x80\x52'
                    patches_applied += 1
                    print(f"     ✅ 修改返回值在 0x{i:x}: false -> true")
                
                # 查找其他可能的失败返回模式
                elif data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0
                    # 检查下一条指令是否是返回相关
                    next_instr = data[i+4:i+8] if i+8 <= end else b'\x00\x00\x00\x00'
                    if (next_instr == b'\xc0\x03\x5f\xd6' or  # ret
                        next_instr[3] & 0xf0 == 0x10):       # 可能的跳转
                        
                        data[i:i+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                        patches_applied += 1
                        print(f"     ✅ 修改状态设置在 0x{i:x}: 0 -> 1")
        
        return patches_applied
    
    def patch_timelock_class(self, data):
        """专门处理TimeLock类"""
        print("\n🎯 处理TimeLock类...")
        
        timelock_pos = data.find(b'TimeLock')
        if timelock_pos == -1:
            print("⚠️  未找到TimeLock类")
            return 0
        
        print(f"✅ 找到TimeLock类在: 0x{timelock_pos:x}")
        
        patches_applied = 0
        
        # 在TimeLock类附近查找showAlert相关函数
        search_start = max(0, timelock_pos - 10000)
        search_end = min(len(data), timelock_pos + 10000)
        
        # 查找showAlert字符串
        alert_patterns = [b'showAlert', b'alert']
        
        for pattern in alert_patterns:
            pos = data.find(pattern, search_start)
            if pos != -1 and pos < search_end:
                print(f"   🔍 找到 {pattern.decode()} 在: 0x{pos:x}")
                
                # 在showAlert附近查找函数实现
                func_start = max(search_start, pos - 2000)
                func_end = min(search_end, pos + 2000)
                
                # 查找函数中的返回值设置
                for i in range(func_start, func_end - 8, 4):
                    # 查找显示弹窗的逻辑，将其改为直接返回
                    if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                        data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                        
                        # 改为直接返回成功，不显示弹窗
                        data[i:i+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                        patches_applied += 1
                        print(f"     ✅ 绕过弹窗显示在 0x{i:x}")
                    
                    # 查找可能的弹窗显示调用，将其NOP掉
                    elif data[i:i+4][3] == 0x94:  # bl指令
                        # 将函数调用改为NOP
                        data[i:i+4] = b'\x1f\x20\x03\xd5'  # nop
                        patches_applied += 1
                        print(f"     ✅ 禁用函数调用在 0x{i:x}")
        
        return patches_applied
    
    def patch_base64_verification(self, data):
        """处理base64验证逻辑"""
        print("\n🔧 处理base64验证逻辑...")
        
        base64_pos = data.find(b'base64EncodedStringWithOptions:')
        if base64_pos == -1:
            print("⚠️  未找到base64相关代码")
            return 0
        
        print(f"✅ 找到base64编码在: 0x{base64_pos:x}")
        
        patches_applied = 0
        
        # base64通常用于验证码的编码/解码
        # 在其附近查找验证逻辑
        search_start = max(0, base64_pos - 3000)
        search_end = min(len(data), base64_pos + 3000)
        
        for i in range(search_start, search_end - 8, 4):
            # 查找比较指令后的条件跳转
            if data[i:i+4] == b'\x1f\x00\x00\x6b':  # cmp w0, #0
                # 查找后续的条件跳转
                next_instr = data[i+4:i+8]
                if next_instr[3] & 0x50 == 0x50:  # 条件跳转指令
                    # 将条件跳转改为无条件通过
                    data[i+4:i+8] = b'\x1f\x20\x03\xd5'  # nop
                    patches_applied += 1
                    print(f"     ✅ 绕过验证检查在 0x{i:x}")
        
        return patches_applied
    
    def apply_precise_patches(self):
        """应用精确补丁"""
        print("\n🚀 开始精确补丁...")
        
        # 读取文件
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        total_patches = 0
        
        # 1. 查找并修改verify函数
        verify_refs = self.find_verify_function(data)
        if verify_refs:
            total_patches += self.patch_verify_functions(data, verify_refs)
        
        # 2. 处理TimeLock类
        total_patches += self.patch_timelock_class(data)
        
        # 3. 处理base64验证
        total_patches += self.patch_base64_verification(data)
        
        # 4. 通用的成功返回补丁
        print("\n🔧 应用通用成功补丁...")
        
        # 查找所有可能的验证失败返回
        offset = 0
        while True:
            # 查找 mov w0, #0; ret 模式
            pattern = b'\x00\x00\x80\x52\xc0\x03\x5f\xd6'
            pos = data.find(pattern, offset)
            if pos == -1:
                break
            
            # 检查上下文是否与验证相关
            context_start = max(0, pos - 1000)
            context_end = min(len(data), pos + 1000)
            context = data[context_start:context_end]
            
            # 如果上下文中有验证相关的内容，就修改
            if (b'verify' in context or b'TimeLock' in context or 
                b'auth' in context or b'check' in context):
                
                # 改为返回成功
                data[pos:pos+4] = b'\x20\x00\x80\x52'  # mov w0, #1
                total_patches += 1
                print(f"   ✅ 通用补丁在 0x{pos:x}")
            
            offset = pos + 1
        
        if total_patches > 0:
            # 保存修改后的文件
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            
            print(f"\n✅ 总共应用了 {total_patches} 个精确补丁")
            
            # 重新签名
            self.resign_file()
            
            return True
        else:
            print("\n❌ 未找到可修改的验证逻辑")
            return False
    
    def resign_file(self):
        """重新签名"""
        try:
            subprocess.run(['codesign', '--remove-signature', str(self.dylib_path)], 
                         check=False, capture_output=True)
            subprocess.run(['codesign', '-f', '-s', '-', str(self.dylib_path)], 
                         capture_output=True)
            print("✅ 重新签名完成")
        except:
            print("⚠️  签名可能有问题，但文件仍可使用")
    
    def restore_backup(self):
        """恢复备份"""
        if self.backup_path.exists():
            shutil.copy2(self.backup_path, self.dylib_path)
            print("✅ 已恢复原始文件")
            return True
        return False

def main():
    print("=" * 60)
    print("🎯 精确验证码绕过工具")
    print("基于实际分析结果的精确补丁")
    print("=" * 60)
    
    dylib_path = sys.argv[1] if len(sys.argv) > 1 else "hhhhsd.dylib"
    
    try:
        bypass = PreciseBypass(dylib_path)
        
        print("\n📋 选择操作:")
        print("1. 🎯 应用精确补丁")
        print("2. 🔄 恢复原始文件")
        print("0. 退出")
        
        choice = input("\n请选择 (0-2): ").strip()
        
        if choice == "1":
            print("\n⚠️  即将应用精确的验证码绕过补丁")
            print("基于实际分析的verify函数和TimeLock类")
            confirm = input("确认继续？(y/N): ").strip().lower()
            
            if confirm == 'y':
                if bypass.backup_file() and bypass.apply_precise_patches():
                    print("\n🎉 精确补丁应用成功！")
                    print("\n📱 使用说明:")
                    print("1. 重启Sky-iOS-Gold应用")
                    print("2. verify函数现在返回成功")
                    print("3. TimeLock弹窗被绕过")
                    print("4. 外挂插件可直接运行")
                else:
                    print("\n❌ 补丁应用失败")
            else:
                print("❌ 用户取消操作")
                
        elif choice == "2":
            bypass.restore_backup()
            
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
    
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
