#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码直接补丁工具
专门修改验证函数，让它们直接返回成功，外挂插件可直接运行

重点：
- 找到验证码检查函数
- 修改返回值为成功(true/1)
- 确保外挂插件接收到成功信号
- 跳过所有验证码弹窗

版本：1.0
"""

import os
import sys
import struct
import shutil
from pathlib import Path
import subprocess
import re

class VerificationPatcher:
    def __init__(self, dylib_path="hhhhsd.dylib"):
        self.dylib_path = Path(dylib_path)
        self.backup_path = Path(str(dylib_path) + ".backup")
        self.patches_applied = []
        
        print("🎯 验证码直接补丁工具")
        print(f"🎯 目标: {self.dylib_path}")
    
    def backup_file(self):
        """备份原始文件"""
        if not self.dylib_path.exists():
            print(f"❌ 文件不存在: {self.dylib_path}")
            return False
        
        if not self.backup_path.exists():
            shutil.copy2(self.dylib_path, self.backup_path)
            print(f"✅ 已备份: {self.backup_path}")
        else:
            print(f"ℹ️  备份已存在: {self.backup_path}")
        
        return True
    
    def find_verification_patterns(self, data):
        """查找验证相关的二进制模式"""
        print("\n🔍 查找验证模式...")
        
        patterns = []
        
        # ARM64 常见的验证失败返回模式
        verification_patterns = [
            # mov w0, #0; ret (返回false)
            (b'\x00\x00\x80\x52\xc0\x03\x5f\xd6', "mov w0, #0; ret"),
            # mov x0, #0; ret
            (b'\x00\x00\x80\xd2\xc0\x03\x5f\xd6', "mov x0, #0; ret"),
            # mov w0, #0; b lr
            (b'\x00\x00\x80\x52\x00\x00\x1f\xd6', "mov w0, #0; br lr"),
        ]
        
        for pattern, desc in verification_patterns:
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                
                # 检查上下文是否与验证相关
                context_start = max(0, pos - 1000)
                context_end = min(len(data), pos + 1000)
                context = data[context_start:context_end]
                
                # 查找验证相关的字符串
                verification_keywords = [
                    b'verify', b'auth', b'code', b'license', b'activate',
                    b'TimeLock', b'showAlert', b'check'
                ]
                
                is_verification = any(keyword in context for keyword in verification_keywords)
                
                if is_verification:
                    patterns.append({
                        'offset': pos,
                        'pattern': pattern,
                        'description': desc,
                        'context_score': sum(1 for kw in verification_keywords if kw in context)
                    })
                    print(f"   🎯 找到验证模式在 0x{pos:x}: {desc}")
                
                offset = pos + 1
        
        # 按上下文相关性排序
        patterns.sort(key=lambda x: x['context_score'], reverse=True)
        return patterns
    
    def patch_return_values(self, data, patterns):
        """修改返回值为成功"""
        print(f"\n🔧 修改 {len(patterns)} 个验证返回值...")
        
        patches_applied = 0
        
        for pattern_info in patterns:
            pos = pattern_info['offset']
            old_pattern = pattern_info['pattern']
            desc = pattern_info['description']
            
            # 根据不同的模式应用相应的补丁
            if b'\x00\x00\x80\x52' in old_pattern:  # mov w0, #0
                # 改为 mov w0, #1 (返回true)
                new_pattern = old_pattern.replace(b'\x00\x00\x80\x52', b'\x20\x00\x80\x52')
            elif b'\x00\x00\x80\xd2' in old_pattern:  # mov x0, #0
                # 改为 mov x0, #1
                new_pattern = old_pattern.replace(b'\x00\x00\x80\xd2', b'\x20\x00\x80\xd2')
            else:
                continue
            
            # 验证位置的模式是否仍然匹配
            if data[pos:pos+len(old_pattern)] == old_pattern:
                data[pos:pos+len(old_pattern)] = new_pattern
                patches_applied += 1
                
                self.patches_applied.append({
                    'offset': pos,
                    'old': old_pattern.hex(),
                    'new': new_pattern.hex(),
                    'description': f"{desc} -> 返回成功"
                })
                
                print(f"   ✅ 0x{pos:x}: {desc} -> 返回成功")
            else:
                print(f"   ⚠️  0x{pos:x}: 模式已变化，跳过")
        
        return patches_applied
    
    def patch_verification_strings(self, data):
        """修改验证相关字符串"""
        print("\n🔧 修改验证字符串...")
        
        patches_applied = 0
        
        # 要修改的验证字符串
        string_patches = [
            # 中文验证相关
            (b'\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81', b'xxxxxxxxx'),  # "验证码"
            (b'\xe9\xaa\x8c\xe8\xaf\x81', b'xxxx'),  # "验证"
            (b'\xe6\xbf\x80\xe6\xb4\xbb\xe7\xa0\x81', b'xxxxxxxxx'),  # "激活码"
            
            # 英文验证相关
            (b'verification', b'xxxxxxxxxxx'),
            (b'activate', b'xxxxxxxx'),
            (b'license', b'xxxxxxx'),
            (b'auth', b'xxxx'),
        ]
        
        for old_str, new_str in string_patches:
            offset = 0
            while True:
                pos = data.find(old_str, offset)
                if pos == -1:
                    break
                
                data[pos:pos+len(old_str)] = new_str
                patches_applied += 1
                print(f"   🔧 替换字符串在 0x{pos:x}: {old_str} -> {new_str}")
                
                offset = pos + len(old_str)
        
        return patches_applied
    
    def patch_timelock_class(self, data):
        """专门修改TimeLock类的验证逻辑"""
        print("\n🎯 修改TimeLock类验证逻辑...")
        
        patches_applied = 0
        
        # 查找TimeLock类相关的代码段
        timelock_pos = data.find(b'TimeLock')
        if timelock_pos == -1:
            print("   ⚠️  未找到TimeLock类")
            return 0
        
        print(f"   🔍 找到TimeLock类在 0x{timelock_pos:x}")
        
        # 在TimeLock类附近查找验证逻辑
        search_start = max(0, timelock_pos - 5000)
        search_end = min(len(data), timelock_pos + 5000)
        
        # 查找showAlert相关的函数
        alert_patterns = [
            b'showAlert',
            b'showAlert2'
        ]
        
        for pattern in alert_patterns:
            pos = data.find(pattern, search_start)
            if pos != -1 and pos < search_end:
                print(f"   🎯 找到 {pattern.decode()} 在 0x{pos:x}")
                
                # 在函数附近查找返回false的模式
                func_start = max(search_start, pos - 1000)
                func_end = min(search_end, pos + 1000)
                
                # 查找并修改返回值
                for i in range(func_start, func_end - 8):
                    # mov w0, #0; ret
                    if (data[i:i+4] == b'\x00\x00\x80\x52' and 
                        data[i+4:i+8] == b'\xc0\x03\x5f\xd6'):
                        
                        # 改为 mov w0, #1; ret (返回成功)
                        data[i:i+4] = b'\x20\x00\x80\x52'
                        patches_applied += 1
                        print(f"     ✅ 修改返回值在 0x{i:x}")
        
        return patches_applied
    
    def create_success_callback(self, data):
        """创建成功回调，确保外挂插件接收到成功信号"""
        print("\n📡 确保插件接收成功信号...")
        
        patches_applied = 0
        
        # 查找可能的回调或状态设置
        callback_keywords = [
            b'callback',
            b'result',
            b'status',
            b'success',
            b'complete'
        ]
        
        for keyword in callback_keywords:
            pos = data.find(keyword)
            if pos != -1:
                print(f"   🔍 找到 {keyword.decode()} 在 0x{pos:x}")
                
                # 在附近查找状态设置代码
                search_start = max(0, pos - 500)
                search_end = min(len(data), pos + 500)
                
                for i in range(search_start, search_end - 4):
                    # 查找设置失败状态的代码
                    if data[i:i+4] == b'\x00\x00\x80\x52':  # mov w0, #0
                        data[i:i+4] = b'\x20\x00\x80\x52'   # mov w0, #1
                        patches_applied += 1
                        print(f"     ✅ 修改状态为成功在 0x{i:x}")
        
        return patches_applied
    
    def apply_patches(self):
        """应用所有补丁"""
        print("\n🚀 开始应用补丁...")
        
        # 读取文件
        with open(self.dylib_path, 'rb') as f:
            data = bytearray(f.read())
        
        original_size = len(data)
        total_patches = 0
        
        # 1. 查找并修改验证返回值
        patterns = self.find_verification_patterns(data)
        total_patches += self.patch_return_values(data, patterns)
        
        # 2. 修改验证字符串
        total_patches += self.patch_verification_strings(data)
        
        # 3. 专门处理TimeLock类
        total_patches += self.patch_timelock_class(data)
        
        # 4. 确保插件接收成功信号
        total_patches += self.create_success_callback(data)
        
        if total_patches > 0:
            # 保存修改后的文件
            with open(self.dylib_path, 'wb') as f:
                f.write(data)
            
            print(f"\n✅ 总共应用了 {total_patches} 个补丁")
            print(f"📊 文件大小: {original_size} -> {len(data)} 字节")
            
            # 重新签名
            self.resign_file()
            
            return True
        else:
            print("\n❌ 未找到可修改的验证逻辑")
            return False
    
    def resign_file(self):
        """重新签名文件"""
        print("\n✍️  重新签名文件...")
        
        try:
            subprocess.run(['codesign', '--remove-signature', str(self.dylib_path)], 
                         check=False, capture_output=True)
            
            result = subprocess.run(['codesign', '-f', '-s', '-', str(self.dylib_path)], 
                                  capture_output=True)
            
            if result.returncode == 0:
                print("✅ 重新签名成功")
            else:
                print("⚠️  签名可能有问题，但文件仍可使用")
                
        except Exception as e:
            print(f"⚠️  签名失败: {e}")
    
    def show_patch_summary(self):
        """显示补丁摘要"""
        if self.patches_applied:
            print(f"\n📋 补丁摘要 ({len(self.patches_applied)} 个):")
            for i, patch in enumerate(self.patches_applied, 1):
                print(f"   {i}. 0x{patch['offset']:x}: {patch['description']}")
                print(f"      {patch['old']} -> {patch['new']}")
        
        print(f"\n🎯 效果:")
        print("✅ 验证码检查将直接返回成功")
        print("✅ 外挂插件可以直接运行")
        print("✅ 无需输入验证码")
        print("✅ 所有功能正常可用")
    
    def restore_backup(self):
        """恢复备份"""
        if self.backup_path.exists():
            shutil.copy2(self.backup_path, self.dylib_path)
            print("✅ 已恢复原始文件")
            return True
        else:
            print("❌ 未找到备份文件")
            return False

def main():
    print("=" * 60)
    print("🎯 验证码直接补丁工具")
    print("让外挂插件直接运行，验证码自动返回成功")
    print("=" * 60)
    
    dylib_path = sys.argv[1] if len(sys.argv) > 1 else "hhhhsd.dylib"
    
    try:
        patcher = VerificationPatcher(dylib_path)
        
        print("\n📋 选择操作:")
        print("1. 应用验证码绕过补丁（推荐）")
        print("2. 恢复原始文件")
        print("3. 显示文件信息")
        print("0. 退出")
        
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            print("\n⚠️  即将修改验证逻辑，让外挂插件直接运行")
            confirm = input("确认继续？(y/N): ").strip().lower()
            
            if confirm == 'y':
                if patcher.backup_file() and patcher.apply_patches():
                    patcher.show_patch_summary()
                    print("\n🎉 补丁应用成功！")
                    print("\n📱 使用说明:")
                    print("1. 重启Sky-iOS-Gold应用")
                    print("2. 外挂插件现在可以直接运行")
                    print("3. 验证码检查会自动返回成功")
                    print("4. 享受无验证码的使用体验！")
                else:
                    print("\n❌ 补丁应用失败")
            else:
                print("❌ 用户取消操作")
                
        elif choice == "2":
            patcher.restore_backup()
            
        elif choice == "3":
            if Path(dylib_path).exists():
                size = Path(dylib_path).stat().st_size
                print(f"📁 文件: {dylib_path}")
                print(f"📊 大小: {size} 字节")
                
                backup_path = Path(dylib_path + ".backup")
                if backup_path.exists():
                    backup_size = backup_path.stat().st_size
                    print(f"💾 备份: {backup_path} ({backup_size} 字节)")
                else:
                    print("💾 备份: 不存在")
            else:
                print(f"❌ 文件不存在: {dylib_path}")
                
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
