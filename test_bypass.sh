#!/bin/bash
# test_bypass.sh
# TimeLock验证码绕过测试脚本
# 
# 功能：验证绕过效果和调试问题
# 版本：1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
show_header() {
    echo "=================================================================="
    echo "🧪 TimeLock验证码绕过测试脚本"
    echo "=================================================================="
    echo ""
}

# 检查设备连接
check_device() {
    log_info "检查iOS设备连接..."
    
    if ! command -v idevice_id &> /dev/null; then
        log_error "未找到idevice_id工具，请安装libimobiledevice"
        echo "安装命令: brew install libimobiledevice"
        return 1
    fi
    
    local devices=$(idevice_id -l)
    if [ -z "$devices" ]; then
        log_error "未检测到连接的iOS设备"
        echo "请确保："
        echo "1. 设备已连接到电脑"
        echo "2. 设备已信任此电脑"
        echo "3. 设备已越狱"
        return 1
    fi
    
    local device_id=$(echo "$devices" | head -n1)
    log_success "检测到设备: $device_id"
    
    # 检查设备信息
    if command -v ideviceinfo &> /dev/null; then
        local device_name=$(ideviceinfo -k DeviceName 2>/dev/null || echo "未知设备")
        local ios_version=$(ideviceinfo -k ProductVersion 2>/dev/null || echo "未知版本")
        log_info "设备名称: $device_name"
        log_info "iOS版本: $ios_version"
    fi
    
    return 0
}

# 检查应用是否运行
check_app_running() {
    log_info "检查Sky-iOS-Gold应用状态..."
    
    if ! command -v idevicedebug &> /dev/null; then
        log_warning "未找到idevicedebug工具，无法检查应用状态"
        return 0
    fi
    
    # 这里可以添加检查应用是否运行的逻辑
    log_info "请手动确认Sky-iOS-Gold应用正在运行"
    return 0
}

# 检查Tweak是否加载
check_tweak_loaded() {
    log_info "检查TimeLockBypass Tweak是否已加载..."
    
    if ! command -v idevicesyslog &> /dev/null; then
        log_error "未找到idevicesyslog工具"
        return 1
    fi
    
    log_info "正在检查系统日志中的TimeLockBypass标记..."
    
    # 启动日志监控（后台运行）
    local log_file="/tmp/timelock_bypass_test.log"
    idevicesyslog | grep -i "timelock\|bypass" > "$log_file" &
    local log_pid=$!
    
    echo "日志监控已启动 (PID: $log_pid)"
    echo "请在另一个终端运行以下命令查看实时日志："
    echo "  tail -f $log_file"
    echo ""
    echo "或者直接查看TimeLockBypass日志："
    echo "  idevicesyslog | grep TimeLockBypass"
    
    # 等待用户操作
    echo ""
    read -p "请启动Sky-iOS-Gold应用，然后按Enter继续..." -r
    
    # 停止日志监控
    kill $log_pid 2>/dev/null || true
    
    # 检查日志内容
    if [ -f "$log_file" ] && [ -s "$log_file" ]; then
        log_success "检测到TimeLockBypass相关日志："
        echo "----------------------------------------"
        cat "$log_file"
        echo "----------------------------------------"
        rm -f "$log_file"
        return 0
    else
        log_warning "未检测到TimeLockBypass日志"
        log_info "可能的原因："
        echo "1. Tweak未正确安装"
        echo "2. 应用未重启"
        echo "3. Hook目标不正确"
        rm -f "$log_file"
        return 1
    fi
}

# 测试验证码弹窗拦截
test_verification_popup() {
    log_info "测试验证码弹窗拦截效果..."
    
    echo ""
    echo "📱 请在Sky-iOS-Gold应用中执行以下操作："
    echo "1. 尝试触发验证码弹窗（如使用外挂功能）"
    echo "2. 观察是否还有验证码弹窗出现"
    echo "3. 检查应用功能是否正常使用"
    echo ""
    
    # 提供测试选项
    echo "请选择测试结果："
    echo "1. 验证码弹窗已被成功拦截，功能正常"
    echo "2. 验证码弹窗仍然出现"
    echo "3. 应用崩溃或异常"
    echo "4. 跳过此测试"
    
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            log_success "🎉 验证码绕过测试成功！"
            return 0
            ;;
        2)
            log_error "❌ 验证码弹窗仍然出现"
            echo "可能的解决方案："
            echo "1. 检查Hook目标类名是否正确"
            echo "2. 尝试重新安装Tweak"
            echo "3. 检查应用版本是否匹配"
            return 1
            ;;
        3)
            log_error "❌ 应用出现异常"
            echo "建议操作："
            echo "1. 恢复原始文件: make restore"
            echo "2. 检查Hook代码是否有问题"
            echo "3. 查看崩溃日志"
            return 1
            ;;
        4)
            log_warning "跳过弹窗拦截测试"
            return 0
            ;;
        *)
            log_error "无效选择"
            return 1
            ;;
    esac
}

# 收集调试信息
collect_debug_info() {
    log_info "收集调试信息..."
    
    local debug_file="timelock_bypass_debug.txt"
    
    {
        echo "TimeLock验证码绕过调试信息"
        echo "生成时间: $(date)"
        echo "==============================="
        echo ""
        
        echo "1. 文件检查:"
        echo "TimeLockBypass.x: $([ -f "TimeLockBypass.x" ] && echo "存在" || echo "不存在")"
        echo "Makefile: $([ -f "Makefile" ] && echo "存在" || echo "不存在")"
        echo "hhhhsd.dylib: $([ -f "hhhhsd.dylib" ] && echo "存在" || echo "不存在")"
        echo "hhhhsd.dylib.backup: $([ -f "hhhhsd.dylib.backup" ] && echo "存在" || echo "不存在")"
        echo ""
        
        echo "2. 编译产物:"
        if [ -d ".theos" ]; then
            find .theos -name "*.dylib" -o -name "*.deb" | head -10
        else
            echo "未找到.theos目录"
        fi
        echo ""
        
        echo "3. 设备信息:"
        if command -v ideviceinfo &> /dev/null; then
            ideviceinfo -k DeviceName 2>/dev/null || echo "无法获取设备名称"
            ideviceinfo -k ProductVersion 2>/dev/null || echo "无法获取iOS版本"
        else
            echo "未安装ideviceinfo工具"
        fi
        echo ""
        
        echo "4. 最近的系统日志 (TimeLockBypass相关):"
        if command -v idevicesyslog &> /dev/null; then
            timeout 5 idevicesyslog | grep -i "timelock\|bypass" | tail -20 || echo "未找到相关日志"
        else
            echo "未安装idevicesyslog工具"
        fi
        
    } > "$debug_file"
    
    log_success "调试信息已保存到: $debug_file"
}

# 显示使用建议
show_recommendations() {
    echo ""
    echo "💡 使用建议："
    echo "1. 首次使用前请备份原始文件"
    echo "2. 如果绕过失效，尝试重新编译安装"
    echo "3. 应用更新后可能需要重新分析和适配"
    echo "4. 遇到问题时查看系统日志获取详细信息"
    echo ""
    echo "🔧 常用命令："
    echo "  查看实时日志: idevicesyslog | grep TimeLockBypass"
    echo "  重新编译: make clean && make"
    echo "  重新安装: make install"
    echo "  恢复备份: make restore"
    echo ""
}

# 主函数
main() {
    show_header
    
    # 检查基本环境
    if ! check_device; then
        log_error "设备检查失败，退出测试"
        exit 1
    fi
    
    echo ""
    check_app_running
    
    echo ""
    if check_tweak_loaded; then
        echo ""
        test_verification_popup
    else
        log_warning "Tweak加载检查失败，但仍可继续测试"
    fi
    
    echo ""
    collect_debug_info
    
    show_recommendations
    
    log_success "测试脚本执行完成"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
